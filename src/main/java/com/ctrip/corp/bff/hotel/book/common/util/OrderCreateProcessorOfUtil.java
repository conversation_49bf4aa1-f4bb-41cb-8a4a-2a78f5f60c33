package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckRcResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.approve.ws.contract.Accreditor;
import com.ctrip.corp.approve.ws.contract.ApproveObjInfo;
import com.ctrip.corp.approve.ws.contract.CorpBookingInfo;
import com.ctrip.corp.approve.ws.contract.FlowTmpl;
import com.ctrip.corp.approve.ws.contract.HotelOrderInfo;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowRequestType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.approve.ws.contract.OrderAmountItem;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo.CityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.OriginalOrderInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ApprovalInfoResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.InsuranceRoutAmountResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PayAmountResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PaymentItemTypeResult;
import com.ctrip.corp.bff.framework.specific.common.entity.ApproverOutEntity;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.CheckCostCenterPassenger;
import com.ctrip.corp.bff.framework.specific.common.utils.ApprovalFlowUtil;
import com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.encrypt.MD5Utils;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.threadlocal.CorpMobileContext;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadLocalProvider;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.Approver;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.NationalityInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerName;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.ApprovalFlowReuseConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.DistinguishReservationEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.InvoiceEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateCertificateTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.signature.AccreditorSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.ApproveObjInfoSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.ApproveObjSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.CorpBookingInfoSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.CostCenterSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.MatchApprovalSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.OrderAmountItemSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.RcItemSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.TravelerInfoSignatureBO;
import com.ctrip.corp.bff.hotel.book.common.signature.param.BookInfoBO;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.*;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response.MapperOfApprovalFlowReuseResponse;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.certificate.CertificateInitConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.certificate.TravellerGivenNameConfig;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigTypeEnum;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import com.ctrip.corp.hotelbooking.hotelws.entity.AmountInfoType;
import com.ctrip.corp.hotelbooking.hotelws.entity.ClientEntity;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderAmountType;
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderPaymentType;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubTransactionInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.TransactionInfo;
import com.ctrip.soa._20183.ApprovalInfoType;
import com.ctrip.soa._20183.CostCenterInfoType;
import com.ctrip.soa._20183.PassengerCostCenterInfoType;
import com.ctrip.soa._20183.SaveCommonDataRequestType;
import com.ctrip.soa._20183.SaveCommonDataResponseType;
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType;
import com.ctrip.soa._20184.OriginalOrderInfoType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelAreaInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderBasicInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.OrderFoundationDataInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import corp.user.service.CorpAccountQueryService.GetAuthDelayResponseType;
import corp.user.service.CorpAccountQueryService.HotelAuthDelayConfig;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardResponseType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.framework.template.common.utils.StringUtil.equalsIgnoreCase;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken;

/**
 * <AUTHOR>
 * @Description 创单流程工具类
 * @Date 2024/8/12 13:12
 * @Version 1.0
 */
public class OrderCreateProcessorOfUtil {
    /**
     * 海外酒店
     */
    public static final String OVERSEAS = "I";
    /**
     * 国内酒店
     */
    public static final String DOMESTIC = "N";
    /**
     * 开关开启
     */
    public static final String SWITCH_ON = "T";
    /**
     * 出差申请A， 提前审批B
     */
    private static final String BILL_CONTROL_MODE_B = "B";

    private static final String NATIONALITY_CODE_CN = "CN";
    private static final String NATIONALITY_CODE_HK = "HK";
    private static final String NATIONALITY_CODE_MO = "MO";
    private static final String NATIONALITY_CODE_TW = "TW";
    private static final String GDSTYPE_AMADUES = "Amadues";

    public static final String VERSION = "3";

    private static final String CREATE_ORDER_PAYTYPE = "PERSONAL";
    private static final String ACCNT = "ACCNT";

    public static final String BOOK_PAY = "bookPay";
    private static final String HOTEL_INVOICE_TYPE_ROOM = "ROOM";

    private static final String RMB = "RMB";
    private static final String CNY = "CNY";
    // 授权状态（S:启动成功 P:审批中 T:审批通过 F:审批否决 C:审批终止 A:无需审批（等同T） ）
    private static final String APPROVAL_STATUS_PASS = "T";
    private static final String APPROVAL_STATUS_DEFAULT_PASS = "A";
    // 行程状态（N：未提交 Submitted：已提交 WaitAuthorize：待授权 Processing：处理中 Canceled：已取消 Successful：已成交）
    private static final String TRIP_STATUS_UN_SUBMITTED = "N";
    private static final String TRIP_STATUS_SUCCESSFUL = "Successful";
    private static final String TRIP_STATUS_CANCELED = "Canceled";
    private static final String TENCENT_OFFLINE = "TENCENT_OFFLINE";
    private static final String OPEN = "1";

    private static final String CODE_MAPPING_CONFIG_CREATE_ORDER = "corphotelbookserviceclient.createorder";
    private static int PRODUCT_LINE_TRIP = 10;

    // 客户选择-不沿用
    private static final String NOT_REUSE = "NOT_REUSE";
    /**
     * 是否结束创单流程
     *
     * @param tuple2
     * @return
     */
    public static boolean endOfProcess(Tuple2<Boolean, OrderCreateResponseType> tuple2) {
        return tuple2 != null && BooleanUtils.isTrue(tuple2.getT1());
    }

    public static OrderCreateResponseType getResponse(Tuple2<Boolean, OrderCreateResponseType> tuple2,
        CityBaseInfo cityBaseInfo) {
        if (tuple2 == null) {
            return null;
        }
        OrderCreateResponseType orderCreateResponseType = tuple2.getT2();
        if (orderCreateResponseType != null) {
            orderCreateResponseType.setCityInfo(
                Optional.ofNullable(cityBaseInfo).map(CityBaseInfo::getCityInfo).orElse(null));
        }
        return tuple2.getT2();
    }

    /**
     * 是否需要同行人管控
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @return
     */
    public static boolean requireVerifyFellowControl(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        // 因私不需要管控
        CorpPayInfo corpPayInfo = orderCreateRequestType.getCorpPayInfo();
        if (CorpPayInfoUtil.isPrivate(corpPayInfo)) {
            return false;
        }

        // 出行人同行程开关 政策执行人同行程开关 都没开启不需要管控, 有一个开启就需要管控
        Integer cityId = Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(0);
        boolean isOverSea = CityInfoUtil.oversea(cityId);

        return accountInfo.bookPolicyPsgMustSameTripApprove(isOverSea, corpPayInfo) || accountInfo.preApprovalSameTrip(
            isOverSea, corpPayInfo);
    }

    /**
     * 是否需要审批流匹配
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @return
     */
    public static boolean requireApprovalFlowMatch(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken,
        Map<String, StrategyInfo> strategyInfos, SearchTripDetailResponseType searchTripDetailResponseType,
        SearchTripDetailResponseType flowSearchTripDetailResponseType) {
        if (Optional.ofNullable(orderCreateToken).map(OrderCreateToken::getContinueTypes).orElse(new ArrayList<>())
            .contains(ContinueTypeConst.SINGLE_APPROVAL_FLOW)) {
            return false;
        }
        if (Optional.ofNullable(orderCreateToken).map(OrderCreateToken::getContinueTypes).orElse(new ArrayList<>())
            .contains(ContinueTypeConst.APPROVAL_FLOW_REUSE)) {
            if (ApprovalFlowReuseConstant.REUSE.equalsIgnoreCase(
                Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                    .orElse(null))) {
                return false;
            }
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return true;
        }
        // 行程不支持
        if (accountInfo.isPackageEnabled()) {
            if (StrategyOfBookingInitUtil.tripPassNeedSingleApproval(strategyInfos)) {
                return requireApprovalFlowMatchOfTrip(searchTripDetailResponseType, flowSearchTripDetailResponseType,
                    orderCreateRequestType, orderCreateToken);
            }
            return false;
        }
        // 单订单：如果客户选择了延用且延用单号非空，无需弹审批流
        if (followApproval(orderCreateRequestType, orderCreateToken)) {
            return false;
        }
        return true;
    }

    private static boolean requireApprovalFlowMatchOfTrip(SearchTripDetailResponseType searchTripDetailResponseType,
        SearchTripDetailResponseType flowSearchTripDetailResponseType, OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        if (buildIsTripFollow(orderCreateRequestType, orderCreateToken)) {
            return buildTripApprovalPassAndUnSuccess(flowSearchTripDetailResponseType);
        }
        if (buildTripApprovalPassAndUnSuccess(searchTripDetailResponseType)) {
            return true;
        }
        return false;
    }

    /**
     * 是否行程追加单场景=行程审批通过&未完成
     * 行程审批状态T,A审批通过
     * 且 tripStatus行程状态： 不是（已成交Successful,已取消Canceled,未提交N）
     *
     * @return
     */
    private static boolean buildTripApprovalPassAndUnSuccess(
        SearchTripDetailResponseType useSearchTripDetailResponseType) {
        if (useSearchTripDetailResponseType == null || useSearchTripDetailResponseType.getBasicInfo() == null) {
            return false;
        }
        String authorizeStatus = useSearchTripDetailResponseType.getBasicInfo().getAuthorizeStatus();
        if (StringUtil.isBlank(authorizeStatus)) {
            return false;
        }
        String tripStatus = useSearchTripDetailResponseType.getBasicInfo().getTripStatus();
        if (StringUtil.isBlank(tripStatus)) {
            return false;
        }
        if (!StringUtil.equalsIgnoreCase(authorizeStatus, APPROVAL_STATUS_PASS) && !StringUtil.equalsIgnoreCase(
            authorizeStatus, APPROVAL_STATUS_DEFAULT_PASS)) {
            return false;
        }
        if (tripStatus.equalsIgnoreCase(TRIP_STATUS_UN_SUBMITTED) || tripStatus.equalsIgnoreCase(TRIP_STATUS_SUCCESSFUL)
            || tripStatus.equalsIgnoreCase(TRIP_STATUS_CANCELED)) {
            return false;
        }
        return true;
    }

    /**
     * 延用审批
     *
     * @param orderCreateRequestType
     * @param orderCreateToken
     * @return
     */
    private static boolean followApproval(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        return TemplateNumberUtil.getValue(buildFollowOrderNo(orderCreateRequestType, orderCreateToken)) > 0;
    }

    public static Long buildFollowOrderNo(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        if (ApprovalFlowReuseConstant.REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            if (StringUtil.isNotBlank(Optional.ofNullable(orderCreateToken.getFollowApprovalResult())
                .map(FollowApprovalResult::getFollowOrderNo).orElse(null))) {
                return TemplateNumberUtil.parseLong(orderCreateToken.getFollowApprovalResult().getFollowOrderNo());
            }
        }
        if (ApprovalFlowReuseConstant.NOT_REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            return 0L;
        }
        if (!CommonConstant.OPEN.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
                .map(FollowApprovalInfoInput::getFollowSelected).orElse(null))) {
            return 0L;
        }
        if (StringUtil.isNotBlank(Optional.ofNullable(orderCreateToken).map(OrderCreateToken::getFollowApprovalResult)
            .map(FollowApprovalResult::getFollowOrderNo).orElse(null))) {
            return TemplateNumberUtil.getValue(
                TemplateNumberUtil.parseLong(orderCreateToken.getFollowApprovalResult().getFollowOrderNo()));
        }
        // offline单订单人工延用是前端直接调用checkAuthFlow做的校验，没有token二次交互，直接取前端传的值
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() != SourceFrom.Offline) {
            return 0L;
        }
        if (!CommonConstant.OPEN.equalsIgnoreCase(
            orderCreateRequestType.getFollowApprovalInfoInput().getArtificialFollow())) {
            return 0L;
        }
        return TemplateNumberUtil.getValue(TemplateNumberUtil.parseLong(
            Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
                .map(FollowApprovalInfoInput::getFollowOrderId).orElse(null)));
    }

    /**
     * 是否需要审批流校验
     *
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requireApprovalFlowCheck(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getApprovalFlowInput() == null) {
            return false;
        }
        return true;
    }

    /**
     * 是否需要调用CheckTravelPolicy管控
     *
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requireCheckTravelPolicyControl(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType == null) {
            return false;
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        return true;
    }

    /**
     * 是否需要RC或重复订单管控
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @return
     */
    public static boolean requireRCorRepeatOrder(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        if (requireRepeatOrderControl(accountInfo, orderCreateToken)) {
            return true;
        }
        return requireCheckTravelPolicyControl(orderCreateRequestType);
    }

    /**
     * 是否需要重复订单管控
     *
     * @param accountInfo
     * @return
     */
    public static boolean requireRepeatOrderControl(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateToken orderCreateToken) {
        if (accountInfo == null || !SWITCH_ON.equalsIgnoreCase(accountInfo.getRepeatOrderCheck())) {
            return false;
        }
        if (orderCreateToken == null || CollectionUtil.isEmpty(orderCreateToken.getContinueTypes())) {
            return true;
        }
        return orderCreateToken.getContinueTypes().stream().filter(Objects::nonNull).noneMatch(
            t -> Sets.newHashSet(ContinueTypeConst.CONFLICT_ORDER, ContinueTypeConst.CONTROL_BOOKING,
                ContinueTypeConst.PRICE_CHANGE).contains(t));
    }

    /**
     * 是否需要CreateOrder
     *
     * @return
     */
    public static boolean requireCreateOrder(OrderCreateToken orderCreateToken) {
        if (orderCreateToken == null) {
            return true;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.PRICE_CHANGE)) {
            return false;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.CONFIRM_ORDER)) {
            return false;
        }
        return true;
    }

    /**
     * 是否需要客户收银台支付
     *
     * @return
     */
    public static boolean requireDoublePay(OrderCreateRequestType orderCreateRequestType,
        PayConfigResponseType payConfigResponseType) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if ((roomPayType != HotelPayTypeEnum.CORP_PAY && roomPayType != HotelPayTypeEnum.ADVANCE_PAY
            && roomPayType != HotelPayTypeEnum.FLASH_STAY_PAY) || !SourceFrom.H5.equals(
            orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom())) {
            return false;
        }
        return "CustomerPayTriggerToAccnt".equalsIgnoreCase(
            Optional.ofNullable(payConfigResponseType).map(PayConfigResponseType::getPayMode).orElse(null));
    }

    /**
     * 是否需要SubmitOrder
     *
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requireSubmitOrder(OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken,
        OrderCreateToken orderCreateToken) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        HotelPayTypeEnum servicePayType =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        if (roomPayType == null) {
            return false;
        }
        if (roomPayType == HotelPayTypeEnum.FLASH_STAY_PAY) {
            return false;
        }
        if (roomPayType == HotelPayTypeEnum.CORP_PAY) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.PRBAL) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.CORPORATE_CARD_PAY) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.CORP_CREDIT_CARD_GUARANTEE && (servicePayType == null
            || servicePayType == HotelPayTypeEnum.CORP_PAY || servicePayType == HotelPayTypeEnum.NONE)) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.GUARANTEE_CORP_PAY && (servicePayType == null
            || servicePayType == HotelPayTypeEnum.CORP_PAY || servicePayType == HotelPayTypeEnum.NONE)) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.CASH && (servicePayType == null
            || servicePayType == HotelPayTypeEnum.CORP_PAY || servicePayType == HotelPayTypeEnum.NONE)) {
            return true;
        }
        // 变价后是公帐
        if (orderCreateToken.containsContinueType(ContinueTypeConst.PRICE_CHANGE) && MathUtils.isLessOrEqualsZero(
            Optional.ofNullable(orderCreateToken.getCreateOrderResult()).map(CreateOrderResult::getPayAmountResult)
                .map(PayAmountResult::getAmount).orElse(BigDecimal.ZERO))) {
            return true;
        }
        return false;
    }

    /**
     * 是否需要SaveCommonData
     *
     * @return
     */
    public static boolean requireSaveCommonData(OrderCreateToken orderCreateToken) {
        if (orderCreateToken == null) {
            return true;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.PRICE_CHANGE)) {
            return false;
        }
        return true;
    }

    /**
     * 是否需要保存保险发票
     *
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requireCorpVatInvoiceProcess(OrderCreateRequestType orderCreateRequestType) {
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelInvoiceInfos())) {
            return false;
        }
        if (orderCreateRequestType.getHotelInsuranceInput() == null) {
            return false;
        }
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs())) {
            return false;
        }
        HotelInvoiceInfo hotelInvoiceInfo =
            HotelInvoiceUtil.getHotelInvoiceInfoInsurance(orderCreateRequestType.getHotelInvoiceInfos());
        return hotelInvoiceInfo != null && hotelInvoiceInfo.getInvoiceInfo() != null;
    }

    public static DistinguishReservationEnum buildDistinguishReservationEnum(CorpPayInfo corpPayInfo,
        IntegrationSoaRequestType integrationSoaRequestType, List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        if (CorpPayInfoUtil.isPrivate(corpPayInfo)) {
            return DistinguishReservationEnum.NONE;
        }
        // 公司是否开通
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.DISTINGUISH_RESERVATION,
            integrationSoaRequestType.getUserInfo().getCorpId())) {
            return DistinguishReservationEnum.NONE;
        }
        // 未选出行人
        if (CollectionUtil.isEmpty(hotelBookPassengerInputs)) {
            return DistinguishReservationEnum.NONE;
        }
        // 出行人仅含本人
        if (hotelBookPassengerInputs.size() == 1 && StringUtil.equalsIgnoreCase(
            integrationSoaRequestType.getUserInfo().getUserId(),
            hotelBookPassengerInputs.stream().findFirst().map(HotelBookPassengerInput::getHotelPassengerInput)
                .map(HotelPassengerInput::getUid).orElse(null))) {
            return DistinguishReservationEnum.EMPLOYEE_TRAVEL;
        }
        return DistinguishReservationEnum.OPTION;
    }

    /**
     * 是否需要保存保险上一次默认发票信息
     *
     * @return
     */
    public static boolean requireSaveContactInvoiceDefaultInfo(List<HotelInvoiceInfo> hotelInvoiceInfos) {
        if (CollectionUtil.isEmpty(hotelInvoiceInfos)) {
            return false;
        }
        // 取房费的发票信息
        HotelInvoiceInfo hotelInvoiceInfo = hotelInvoiceInfos.stream().filter(Objects::nonNull)
            .filter(hotelInvoice -> HOTEL_INVOICE_TYPE_ROOM.equals(hotelInvoice.getHotelInvoiceType()))
            .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (hotelInvoiceInfo == null || hotelInvoiceInfo.getInvoiceInfo() == null || StringUtil.isBlank(
            hotelInvoiceInfo.getInvoiceInfo().getInvoiceType())) {
            return false;
        }
        InvoiceEnum invoiceEnum = InvoiceEnum.getValueForAgg(hotelInvoiceInfo.getInvoiceInfo().getInvoiceType());
        if (invoiceEnum == InvoiceEnum.E_VAT || invoiceEnum == InvoiceEnum.D_INVOICE) {
            return StringUtil.isNotBlank(hotelInvoiceInfo.getInvoiceInfo().getInvoiceTitle());
        }
        if (StringUtil.isBlank(hotelInvoiceInfo.getInvoiceInfo().getTaxNumber())) {
            return false;
        }
        if (hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo() == null) {
            return false;
        }
        if (StringUtil.isBlank(hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo().getCompanyBank())) {
            return false;
        }
        if (StringUtil.isBlank(hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo().getCompanyBankAccount())) {
            return false;
        }
        if (StringUtil.isBlank(hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo().getCompanyTel())) {
            return false;
        }
        if (StringUtil.isBlank(hotelInvoiceInfo.getInvoiceInfo().getInvoiceTitle())) {
            return false;
        }
        return true;
    }

    /**
     * 是否需要收银台个人支付 非表单支付、非闪住支付、非公帐等直接提交订单那的支付 个人、混付、现付+服务费个人
     *
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requirePaymentOrderCreate(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken, OrderCreateToken orderCreateToken) {
        if (StrategyOfBookingInitUtil.offlineFromPay(orderCreateRequestType.getStrategyInfos())) {
            return false;
        }
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomPayType == HotelPayTypeEnum.FLASH_STAY_PAY) {
            return false;
        }
        return needSelfPay(orderCreateRequestType, resourceToken, orderCreateToken);
    }

    /**
     * 是否需要商户信息 表单支付、收银台支付、闪住
     *
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requireQueryPaymentBill(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken, OrderCreateToken orderCreateToken) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomPayType == HotelPayTypeEnum.FLASH_STAY_PAY) {
            return true;
        }
        return needSelfPay(orderCreateRequestType, resourceToken, orderCreateToken);
    }

    private static boolean needSelfPay(OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken,
        OrderCreateToken orderCreateToken) {
        return !requireSubmitOrder(orderCreateRequestType, resourceToken, orderCreateToken);
    }

    /**
     * 是否需要表单支付
     *
     * @return
     */
    public static boolean requireFromPay(OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken,
        OrderCreateToken orderCreateToken) {
        if (!StrategyOfBookingInitUtil.offlineFromPay(orderCreateRequestType.getStrategyInfos())) {
            return false;
        }
        return needSelfPay(orderCreateRequestType, resourceToken, orderCreateToken);
    }

    /**
     * 是否Offline灰度新支付
     *
     * @return
     */
    public static boolean requireOfflineNewPay(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken, OrderCreateToken orderCreateToken) {
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() != SourceFrom.Offline) {
            return false;
        }
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.OFFLINE_NEW_PAY,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return false;
        }
        return needSelfPay(orderCreateRequestType, resourceToken, orderCreateToken);
    }

    /**
     * 是否需要沿用授权
     *
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requireFollowApproval(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken, GetAuthDelayResponseType getAuthDelayResponseType,
        WrapperOfAccount.AccountInfo accountInfo) {
        if (orderCreateRequestType == null) {
            return false;
        }
        // offline单订单人工延用是前端直接调用checkAuthFlow做的校验，没有token二次交互，无需校验单据是否能延用
        if (buildOfflineSingleArtificialFollow(orderCreateRequestType, accountInfo)) {
            return false;
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.FOLLOW_APPROVAL)) {
            return false;
        }
        if (!Optional.ofNullable(getAuthDelayResponseType).map(GetAuthDelayResponseType::getHotelAuthDelayconfig)
            .map(HotelAuthDelayConfig::isSmartauthdelayCo).orElse(false)) {
            return false;
        }
        return StringUtil.isNotBlank(Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
            .map(FollowApprovalInfoInput::getFollowOrderId).orElse(null));
    }

    /**
     * offline单订单人工延用是前端直接调用checkAuthFlow做的校验，没有token二次交互，直接取前端传的值
     * 这里其实是有不一致风险存在的 先保持老逻辑 提风险给产品 详细调研后再确认如何改造
     *
     * @param orderCreateRequestType
     * @return
     */
    protected static boolean buildOfflineSingleArtificialFollow(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        if (accountInfo.isPackageEnabled()) {
            return false;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() != SourceFrom.Offline) {
            return false;
        }
        if (orderCreateRequestType.getFollowApprovalInfoInput() == null) {
            return false;
        }
        if (!CommonConstant.OPEN.equalsIgnoreCase(
            orderCreateRequestType.getFollowApprovalInfoInput().getArtificialFollow())) {
            return false;
        }
        return StringUtil.isNotBlank(orderCreateRequestType.getFollowApprovalInfoInput().getFollowOrderId());
    }

    /**
     * offline人工行程延用
     *
     * @param orderCreateRequestType
     * @param orderCreateToken
     * @return
     */
    public static boolean requireArtificialFollowApproval(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.FOLLOW_APPROVAL)) {
            return false;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() != SourceFrom.Offline) {
            return false;
        }
        if (orderCreateRequestType.getFollowApprovalInfoInput() == null) {
            return false;
        }
        if (!BooleanUtil.parseStr(true)
            .equalsIgnoreCase(orderCreateRequestType.getFollowApprovalInfoInput().getArtificialFollow())) {
            return false;
        }
        if (StringUtil.isBlank(orderCreateRequestType.getFollowApprovalInfoInput().getFollowTripId())) {
            return false;
        }
        return Long.parseLong(orderCreateRequestType.getFollowApprovalInfoInput().getFollowTripId()) > 0;
    }

    /**
     * 是否需要智能沿用审批
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @param orderCreateToken
     * @return
     */
    public static boolean requireSmartFollowApproval(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken,
        GetAuthDelayResponseType getAuthDelayResponseType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.FOLLOW_APPROVAL)) {
            return false;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Offline) {
            if (!Optional.ofNullable(getAuthDelayResponseType).map(GetAuthDelayResponseType::getHotelAuthDelayconfig)
                .map(HotelAuthDelayConfig::isSmartauthdelayCf).orElse(false)) {
                return false;
            }
            return StringUtil.isBlank(Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
                .map(FollowApprovalInfoInput::getFollowOrderId).orElse(null));
        }
        if (!Optional.ofNullable(getAuthDelayResponseType).map(GetAuthDelayResponseType::getHotelAuthDelayconfig)
            .map(HotelAuthDelayConfig::isSmartauthdelayCo).orElse(false)) {
            return false;
        }
        // APP、PC行程 qconfig范围内的公司 仅支持重新预订的校验延用，不支持智能延用
        if (accountInfo.isPackageEnabled() && QConfigOfCustomConfig.isSupport(
            CustomConfigKeyConstant.SUPPORT_REBOOK_TRIP_APPROVAL_FLOW,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return false;
        }
        return StringUtil.isBlank(Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
            .map(FollowApprovalInfoInput::getFollowOrderId).orElse(null));
    }

    /**
     * 是否需要创建无感行程
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @return
     */
    public static boolean requireCreateTrip(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (!accountInfo.isPackageEnabled()) {
            return false;
        }
        if (getTripId(accountInfo, orderCreateRequestType, null, orderCreateToken) > 0) {
            return false;
        }
        return true;
    }

    public static boolean requireSaveHabitPolicyUser(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        if (!requireCreateTrip(orderCreateRequestType, accountInfo, orderCreateToken)) {
            return false;
        }
        String uid = orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId();
        String policyUid =
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid).orElse(null);
        return StringUtil.isNotBlank(policyUid) && !uid.equalsIgnoreCase(policyUid);
    }

    public static Long getTripId(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType, CreateTripResponseType createTripResponseType,
        OrderCreateToken orderCreateToken) {
        if (!accountInfo.isPackageEnabled()) {
            return 0L;
        }
        if (buildIsTripFollow(orderCreateRequestType, orderCreateToken)) {
            return getfollowTripId(orderCreateRequestType, orderCreateToken);
        }
        if (orderCreateRequestType.getTripInput() != null && StringUtil.isNotBlank(
            orderCreateRequestType.getTripInput().getTripId())
            && TemplateNumberUtil.parseLong(orderCreateRequestType.getTripInput().getTripId()) > 0) {
            return TemplateNumberUtil.getValue(
                TemplateNumberUtil.parseLong(orderCreateRequestType.getTripInput().getTripId()));
        }
        return Optional.ofNullable(createTripResponseType).map(CreateTripResponseType::getTripId).orElse(0L);
    }

    public static boolean buildIsTripFollow(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        if (ApprovalFlowReuseConstant.NOT_REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            return false;
        }
        if (ApprovalFlowReuseConstant.REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            if (StringUtil.isNotBlank(
                Optional.ofNullable(orderCreateToken.getFollowApprovalResult()).map(FollowApprovalResult::getTripId)
                    .orElse(null))) {
                return true;
            }
        }
        if (orderCreateRequestType.getFollowApprovalInfoInput() == null) {
            return false;
        }
        if (!"T".equalsIgnoreCase(orderCreateRequestType.getFollowApprovalInfoInput().getFollowSelected())) {
            return false;
        }
        return StringUtil.isNotBlank(
            Optional.ofNullable(orderCreateToken.getFollowApprovalResult()).map(FollowApprovalResult::getTripId)
                .orElse(null));
    }

    public static long getfollowTripId(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        if (ApprovalFlowReuseConstant.REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            if (StringUtil.isNotBlank(
                Optional.ofNullable(orderCreateToken.getFollowApprovalResult()).map(FollowApprovalResult::getTripId)
                    .orElse(null))) {
                return TemplateNumberUtil.parseLong(orderCreateToken.getFollowApprovalResult().getTripId());
            }
        }
        if (orderCreateRequestType.getFollowApprovalInfoInput() != null && "T".equalsIgnoreCase(
            orderCreateRequestType.getFollowApprovalInfoInput().getFollowSelected()) && StringUtil.isNotBlank(
            Optional.ofNullable(orderCreateToken.getFollowApprovalResult()).map(FollowApprovalResult::getTripId)
                .orElse(null))) {
            return TemplateNumberUtil.getValue(
                TemplateNumberUtil.parseLong(orderCreateToken.getFollowApprovalResult().getTripId()));
        }
        return 0;
    }

    /**
     * 是否需要关联行程
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @return
     */
    public static boolean requireTripMapping(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        return false;
    }

    /**
     * 是否需要审批查询
     *
     * @param orderCreateRequestType
     * @param accountInfo
     * @return
     */
    public static boolean requireSearchApproval(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (StringUtil.isNotBlank(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                .orElse(null))) {
            return true;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(p -> StringUtil.isNotBlank(
            Optional.ofNullable(p.getHotelPassengerInput()).map(HotelPassengerInput::getApprovalInput)
                .map(ApprovalInput::getSubApprovalNo).orElse(null)))) {
            return true;
        }
        return false;
    }

    /**
     * 是否审批单校验管控
     * @param orderCreateRequestType
     * @param accountInfo
     * @param orderCreateToken
     * @return
     */
    public static boolean requireVerifyApproval(OrderCreateRequestType orderCreateRequestType,
                                                WrapperOfAccount.AccountInfo accountInfo,
                                                OrderCreateToken orderCreateToken) {
        if (requireSearchApproval(orderCreateRequestType, accountInfo, orderCreateToken)) {
            return true;
        }
        if (BooleanConstant.TRUE.equalsIgnoreCase(
                Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getApprovalInput).map(ApprovalInput::getEmergency)
                        .orElse(null)) && isAfterApproval(accountInfo,
                CityInfoUtil.oversea(Optional.ofNullable(orderCreateRequestType)
                .map(OrderCreateRequestType::getCityInput).map(CityInput::getCityId).orElse(null)),
                Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getCorpPayInfo).orElse(null))) {
            return true;
        }
        return false;
    }

    /**
     * 是否提前审批后置
     * @param account
     * @param oversea
     * @param corpPayInfo
     * @return
     */
    protected static boolean isAfterApproval(WrapperOfAccount.AccountInfo account, Boolean oversea, CorpPayInfo corpPayInfo) {
        if (account == null) {
            return false;
        }
        // 非提前审批
        if (!account.isPreApprovalRequired(oversea, corpPayInfo)) {
            return false;
        }
        // 非前置
        if (org.apache.commons.lang3.BooleanUtils.isFalse(account.isOaApprovalHead())) {
            return true;
        }
        return false;
    }

    public static boolean requireGetTravelPolicyContext(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        return true;
    }

    public static boolean requireSSOInfoQuery(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        if (orderCreateRequestType.getSsoInput() == null) {
            return false;
        }
        if (StringUtil.isBlank(orderCreateRequestType.getSsoInput().getSsoKey())) {
            return false;
        }
        return true;
    }

    public static String getUseName(HotelBookPassengerInput p, Integer cityId,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        String eName = getEname(p, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap);
        if (usePsgEname(cityId, checkAvailInfo)) {
            return eName;
        }
        if (StringUtil.isBlank(p.getName())) {
            return eName;
        }
        if (StringUtil.isBlank(eName)) {
            return p.getName();
        }
        if (StringUtil.isBlank(
            Optional.ofNullable(p.getNationalityInfo()).map(NationalityInfo::getNationalityCode).orElse(null))) {
            return p.getName();
        }
        if (!isChina(p.getNationalityInfo().getNationalityCode())) {
            return eName;
        }
        return p.getName();
    }

    public static boolean usePsgEname(HotelBookPassengerInput p, Integer cityId,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (usePsgEname(cityId, checkAvailInfo)) {
            return true;
        }
        String eName = getEname(p, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap);
        if (StringUtil.isBlank(p.getName())) {
            return true;
        }
        if (StringUtil.isBlank(eName)) {
            return false;
        }
        if (StringUtil.isBlank(
            Optional.ofNullable(p.getNationalityInfo()).map(NationalityInfo::getNationalityCode).orElse(null))) {
            return false;
        }
        if (!isChina(p.getNationalityInfo().getNationalityCode())) {
            return true;
        }
        return false;
    }

    public static boolean usePsgEname(Integer cityId, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        if (QConfigOfCustomConfig.isSupport("useAggPsgEname",
            Optional.ofNullable(ThreadLocalProvider.get()).map(CorpMobileContext::getCorpId).orElse(null))) {
            if (CollectionUtil.isEmpty(checkAvailInfo.getGuestsNameLanguages())) {
                return false;
            }
            if (checkAvailInfo.getGuestsNameLanguages().stream().noneMatch("zh"::equalsIgnoreCase)) {
                return true;
            }
            return false;
        }
        return CityInfoUtil.oversea(cityId) || CityInfoUtil.hkOrMacao(cityId) || checkAvailInfo.isAmadues();
    }

    private static boolean isChina(String nationalityCode) {
        List<String> chinaNationalityCodes =
            Arrays.asList(NATIONALITY_CODE_CN, NATIONALITY_CODE_HK, NATIONALITY_CODE_MO, NATIONALITY_CODE_TW);
        return chinaNationalityCodes.stream().anyMatch(c -> StringUtil.equalsIgnoreCase(c, nationalityCode));
    }

    public static String getEname(HotelBookPassengerInput hotelBookPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (hotelBookPassengerInput == null) {
            return "";
        }
        // 蓝色空间英文名以证件为准计算
        if (StrategyOfOrderCreateUtil.passengerNameConcatUseConfig(strategyInfoMap)) {
            return buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, qconfigOfCertificateInitConfig);
        }
        return getEname(hotelBookPassengerInput.getPassengerBasicInfo());
    }

    public static NationalityInfo buildNationalityInfo(HotelBookPassengerInput hotelBookPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        if (CollectionUtil.isEmpty(hotelBookPassengerInput.getCertificateInfos())) {
            return hotelBookPassengerInput.getNationalityInfo();
        }
        CertificateInfo useCertificateInfo =
            getDefaultCertificateInfo(hotelBookPassengerInput.getCertificateInfos(), checkAvailInfo);
        if (useCertificateInfo == null) {
            return hotelBookPassengerInput.getNationalityInfo();
        }
        return useCertificateInfo.getNationalityInfo();
    }

    protected static String buildBlueSpaceEname(HotelBookPassengerInput hotelBookPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
        CertificateInfo useCertificateInfo =
            getDefaultCertificateInfo(hotelBookPassengerInput.getCertificateInfos(), checkAvailInfo);
        String legalFirstName =
            Optional.ofNullable(useCertificateInfo).map(CertificateInfo::getLegalFirstName).orElse(null);
        String legalLastName =
            Optional.ofNullable(useCertificateInfo).map(CertificateInfo::getLegalLastName).orElse(null);
        String legalMiddleName =
            Optional.ofNullable(useCertificateInfo).map(CertificateInfo::getLegalMiddleName).orElse(null);
        PassengerName enName =
            Optional.ofNullable(hotelBookPassengerInput.getPassengerBasicInfo()).map(PassengerBasicInfo::getEnName)
                .orElse(null);
        PassengerName localName =
            Optional.ofNullable(hotelBookPassengerInput.getPassengerBasicInfo()).map(PassengerBasicInfo::getLocalName)
                .orElse(null);
        String defaultNameType = Optional.ofNullable(hotelBookPassengerInput.getPassengerBasicInfo())
            .map(PassengerBasicInfo::getDefaultNameType).orElse(null);
        String originEnName = null;
        if (StringUtil.isNotBlank(legalLastName) || StringUtil.isNotBlank(legalFirstName)) {
            originEnName = concatIgnoreBlank("/", legalLastName, legalFirstName);
        } else if (StringUtil.equalsIgnoreCase(defaultNameType, "EN")) {
            if (enName != null && (StringUtil.isNotBlank(enName.getLastName()) || StringUtil.isNotBlank(
                enName.getFirstName()))) {
                originEnName = concatIgnoreBlank("/", enName.getLastName(), enName.getFirstName());
            }
        } else if (StringUtil.equalsIgnoreCase(defaultNameType, "LOCAL")) {
            if (localName != null && (StringUtil.isNotBlank(localName.getLastName()) || StringUtil.isNotBlank(
                localName.getFirstName()))) {
                originEnName = concatIgnoreBlank("/", localName.getLastName(), localName.getFirstName());
            }
        }

        String nationality = Optional.ofNullable(useCertificateInfo).map(CertificateInfo::getNationalityInfo)
            .map(NationalityInfo::getNationalityCode).orElse(
                Optional.ofNullable(hotelBookPassengerInput.getNationalityInfo())
                    .map(NationalityInfo::getNationalityCode).orElse(null));
        return convertEnNameV2(legalLastName, legalMiddleName, legalFirstName, nationality, originEnName,
            qconfigOfCertificateInitConfig);
    }

    private static String convertEnNameV2(String legalLastName, String legalMiddleName, String legalFirstName,
        String nationality, String originEnName, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
        String ename = originEnName;
        CertificateInitConfig certificateInitConfig = Optional.ofNullable(qconfigOfCertificateInitConfig)
            .map(QconfigOfCertificateInitConfig::getCertificateInitConfig).orElse(null);
        // 临时方案，如果命中配置，取新的格式化方式，否则取前端原有的值传值
        String givenNameFormat =
            Optional.ofNullable(certificateInitConfig).map(CertificateInitConfig::getTravellerGivenNameConfig)
                .map(TravellerGivenNameConfig::getNationalityConfig).map(c -> MapUtil.get(c, nationality)).orElse(null);

        if (StringUtil.isNotEmpty(legalLastName) && StringUtil.isNotEmpty(givenNameFormat)) {
            if (StringUtil.isNotEmpty(legalLastName)) {
                ename = getPassengerName(legalLastName, legalMiddleName, legalFirstName, nationality,
                    qconfigOfCertificateInitConfig);
            }
        }
        return ename;
    }

    private static String getPassengerName(String lastName, String middleName, String firstName, String nation,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
        if (StringUtil.isEmpty(lastName) && StringUtil.isEmpty(middleName) && StringUtil.isEmpty(firstName)) {
            return "";
        }

        CertificateInitConfig certificateInitConfig = Optional.ofNullable(qconfigOfCertificateInitConfig)
            .map(QconfigOfCertificateInitConfig::getCertificateInitConfig).orElse(null);

        String givenNameFormat =
            Optional.ofNullable(certificateInitConfig).map(CertificateInitConfig::getTravellerGivenNameConfig)
                .map(c -> {
                    return Optional.ofNullable(MapUtil.get(c.getNationalityConfig(), nation))
                        .orElse(c.getDefaultConfig());
                }).orElseGet(() -> null);

        String givenName = "";
        if (StringUtil.isNotEmpty(givenNameFormat) && StringUtil.isNotEmpty(middleName)) {
            LinkedHashMap<String, String> params = new LinkedHashMap<>();
            params.put("{middleName}", middleName);
            params.put("{firstName}", firstName);
            givenName =
                org.apache.commons.lang.StringUtils.replaceEach(givenNameFormat, params.keySet().toArray(new String[0]),
                    params.values().toArray(new String[0]));
        } else if (StringUtil.isEmpty(middleName)) {
            givenName = firstName;
        }
        return Optional.ofNullable(lastName).orElse("").concat("/").concat(Optional.ofNullable(givenName).orElse(""));
    }

    private static final String EN_NAME_PATTERN = "^[a-zA-Z]+$";

    private static String concatCnOrEnName(String lastName, String firstName) {
        if (StringUtil.isNotBlank(lastName) && lastName.matches(EN_NAME_PATTERN)
                && StringUtil.isNotBlank(firstName) && firstName.matches(EN_NAME_PATTERN)) {
            return concatIgnoreBlank("/", lastName, firstName);
        }
        return concatIgnoreBlank("", lastName, firstName);
    }

    private static String concatIgnoreBlank(String splicer, String... params) {
        if (params == null) {
            return "";
        }

        return Arrays.stream(params).filter(StringUtil::isNotBlank)
            .collect(Collectors.joining(Optional.ofNullable(splicer).orElse("")));
    }

    public static CertificateInfo buildUseCertificateInfo(HotelBookPassengerInput orderCreateHotelPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        if (CollectionUtil.isNotEmpty(orderCreateHotelPassengerInput.getCertificateInfos())) {
            return OrderCreateProcessorOfUtil.getDefaultCertificateInfo(
                orderCreateHotelPassengerInput.getCertificateInfos(), checkAvailInfo);
        }
        return orderCreateHotelPassengerInput.getCertificateInfo();
    }

    /**
     * @param certificateInfoList
     * @return
     */
    private static CertificateInfo getDefaultCertificateInfo(List<CertificateInfo> certificateInfoList,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        if (CollectionUtil.isEmpty(certificateInfoList)) {
            return null;
        }
        // 默认证件
        CertificateInfo defaultCertificateInfo = certificateInfoList.stream()
            .filter(x -> StringUtil.equalsIgnoreCase(BooleanUtil.parseStr(true), x.getDefaultCertificateFlag()))
            .findFirst().orElse(null);

        // 是否无证件预订
        if (CollectionUtil.isEmpty(checkAvailInfo.getSupportCertificateType())) {
            return certificateInfoList.stream().filter(
                x -> StringUtil.equalsIgnoreCase(OrderCreateCertificateTypeEnum.OTHERDOCUMENT.name(),
                    x.getCertificateType())).findFirst().orElse(defaultCertificateInfo);
        }
        return defaultCertificateInfo;
    }

    private static String getEname(PassengerBasicInfo inputPsg) {
        if (inputPsg == null) {
            return "";
        }
        if (StringUtil.isNotBlank(inputPsg.getPreferLastName()) && StringUtil.isNotBlank(inputPsg.getPreferFirstName())
            && StringUtil.isNotBlank(inputPsg.getPreferMiddleName())) {
            return inputPsg.getPreferFirstName() + " " + inputPsg.getPreferMiddleName() + "/"
                + inputPsg.getPreferLastName();
        }
        if (StringUtil.isNotBlank(inputPsg.getPreferLastName()) && StringUtil.isNotBlank(
            inputPsg.getPreferFirstName())) {
            return inputPsg.getPreferLastName() + "/" + inputPsg.getPreferFirstName();
        }
        return "";
    }

    public static String getEname(String firstName, String lastName, String middleName) {
        if (StringUtil.isNotBlank(firstName) && StringUtil.isNotBlank(lastName) && StringUtil.isNotBlank(middleName)) {
            return firstName + " " + middleName + "/" + lastName;
        }
        if (StringUtil.isNotBlank(firstName) && StringUtil.isNotBlank(lastName)) {
            return lastName + "/" + firstName;
        }
        return "";
    }

    /**
     * 获取城市id
     *
     * @param orderCreateRequestType
     * @return
     */
    public static Integer getCityId(OrderCreateRequestType orderCreateRequestType) {
        return Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getCityInput)
            .map(CityInput::getCityId).orElse(0);
    }

    /**
     * 构建签名
     *
     * @param matchApprovalFlowRequest
     * @return
     */
    public static String buildSignature(MatchApprovalFlowRequestType matchApprovalFlowRequest) {
        if (matchApprovalFlowRequest == null) {
            return StringUtilsExt.EMPTY;
        }
        MatchApprovalSignatureBO matchApprovalSignatureBO = buildMatchApprovalSignatureBO(matchApprovalFlowRequest);
        String jsonStr = JsonUtil.toJson(matchApprovalSignatureBO);
        // 记录下加签数据 方便问题查找
        LogUtil.loggingClogOnly(LogLevelEnum.Info, OrderCreateProcessorOfUtil.class, "buildSignature",
            jsonStr, null);
        // 选取关键元素做签名
        try {
            return MD5Utils.md5(jsonStr);
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, OrderCreateProcessorOfUtil.class, "buildSignature",
                jsonStr, null);
        }
        return null;
    }

    public static MatchApprovalSignatureBO buildMatchApprovalSignatureBO(
        MatchApprovalFlowRequestType matchApprovalFlowRequestType) {
        MatchApprovalSignatureBO matchApprovalSignatureBO = new MatchApprovalSignatureBO();
        matchApprovalSignatureBO.setServerFrom(matchApprovalFlowRequestType.getServerFrom());
        matchApprovalSignatureBO.setCompanyId(matchApprovalFlowRequestType.getCompanyId());
        matchApprovalSignatureBO.setUid(matchApprovalFlowRequestType.getUid());
        matchApprovalSignatureBO.setPolicyUserId(matchApprovalFlowRequestType.getPolicyUserId());
        matchApprovalSignatureBO.setApprovalType(matchApprovalFlowRequestType.getApprovalType());
        matchApprovalSignatureBO.setApproveObjInfoSignatureBO(
            buildApproveObjInfoSignatureBO(matchApprovalFlowRequestType.getApproveObjInfos()));
        return matchApprovalSignatureBO;
    }

    protected static ApproveObjInfoSignatureBO buildApproveObjInfoSignatureBO(ApproveObjInfo approveObjInfo) {
        if (approveObjInfo == null) {
            return null;
        }
        ApproveObjInfoSignatureBO approveObjInfoSignatureBO = new ApproveObjInfoSignatureBO();
        approveObjInfoSignatureBO.setCorpBookingInfoSignatureBO(
            buildCorpBookingInfoSignatureBO(approveObjInfo.getCorpBookingInfo()));
        approveObjInfoSignatureBO.setApproveObjSignatureBO(buildApproveObjSignatureBO(approveObjInfo));
        approveObjInfoSignatureBO.setAccreditorSignatureBOS(
            buildAccreditorSignatureBOS(approveObjInfo.getAccreditorList()));
        return approveObjInfoSignatureBO;
    }

    protected static List<AccreditorSignatureBO> buildAccreditorSignatureBOS(List<Accreditor> accreditors) {
        if (CollectionUtil.isEmpty(accreditors)) {
            return null;
        }
        return accreditors.stream().filter(Objects::nonNull).map(a -> {
            AccreditorSignatureBO accreditorSignatureBO = new AccreditorSignatureBO();
            accreditorSignatureBO.setUid(a.getUid());
            accreditorSignatureBO.setLevel(a.getLevel());
            return accreditorSignatureBO;
        }).collect(Collectors.toList());
    }

    protected static OrderAmountItemSignatureBO buildOrderAmountItemSignatureBO(OrderAmountItem orderAmountItem) {
        if (orderAmountItem == null) {
            return null;
        }
        OrderAmountItemSignatureBO orderAmountItemSignatureBO = new OrderAmountItemSignatureBO();
        orderAmountItemSignatureBO.setAmount(orderAmountItem.getAmount());
        orderAmountItemSignatureBO.setCurrency(orderAmountItem.getCurrency());
        return orderAmountItemSignatureBO;
    }

    protected static CorpBookingInfoSignatureBO buildCorpBookingInfoSignatureBO(CorpBookingInfo corpBookingInfo) {
        if (corpBookingInfo == null || corpBookingInfo.getHotelOrderInfo() == null) {
            return null;
        }
        HotelOrderInfo hotelOrderInfo = corpBookingInfo.getHotelOrderInfo();
        CorpBookingInfoSignatureBO corpBookingInfoSignatureBO = new CorpBookingInfoSignatureBO();
        corpBookingInfoSignatureBO.setTravelReason(hotelOrderInfo.getTravelReason());
        corpBookingInfoSignatureBO.setBookingChannel(hotelOrderInfo.getBookingChannel());
        corpBookingInfoSignatureBO.setReachTravel(hotelOrderInfo.isReachTravel());
        corpBookingInfoSignatureBO.setControl(hotelOrderInfo.isControl());
        corpBookingInfoSignatureBO.setContingent(hotelOrderInfo.isContingent());
        corpBookingInfoSignatureBO.setHighRisk(hotelOrderInfo.isHighRisk());
        corpBookingInfoSignatureBO.setHotelType(hotelOrderInfo.getHotelType());
        corpBookingInfoSignatureBO.setStarLevel(hotelOrderInfo.getStarLevel());
        corpBookingInfoSignatureBO.setProductType(hotelOrderInfo.getProductType());
        corpBookingInfoSignatureBO.setBookType(hotelOrderInfo.getBookType());
        corpBookingInfoSignatureBO.setPayType(hotelOrderInfo.getPayType());
        corpBookingInfoSignatureBO.setPersonPay(buildOrderAmountItemSignatureBO(hotelOrderInfo.getPersonPay()));
        corpBookingInfoSignatureBO.setAccountPay(buildOrderAmountItemSignatureBO(hotelOrderInfo.getAccountPay()));
        corpBookingInfoSignatureBO.setTotalPay(buildOrderAmountItemSignatureBO(hotelOrderInfo.getTotalPay()));
        corpBookingInfoSignatureBO.setNightPrice(buildOrderAmountItemSignatureBO(hotelOrderInfo.getNightPrice()));
        corpBookingInfoSignatureBO.setNightAmount(hotelOrderInfo.getNightAmount());
        corpBookingInfoSignatureBO.setOrderAmount(hotelOrderInfo.getOrderAmount());
        corpBookingInfoSignatureBO.setCurrencyType(hotelOrderInfo.getCurrencyType());
        corpBookingInfoSignatureBO.setStandard(hotelOrderInfo.isStandard());
        if (CollectionUtil.isNotEmpty(hotelOrderInfo.getRcItemList())) {
            corpBookingInfoSignatureBO.setRcItemSignatureBOS(
                hotelOrderInfo.getRcItemList().stream().filter(Objects::nonNull).map(rcItem -> {
                    RcItemSignatureBO rcItemSignatureBO = new RcItemSignatureBO();
                    rcItemSignatureBO.setRc(rcItem.getRc());
                    rcItemSignatureBO.setRcType(rcItem.getRcType());
                    return rcItemSignatureBO;
                }).collect(Collectors.toList()));
        }
        corpBookingInfoSignatureBO.setOverStandardStage(hotelOrderInfo.getOverStandardStage());
        return corpBookingInfoSignatureBO;
    }

    protected static ApproveObjSignatureBO buildApproveObjSignatureBO(ApproveObjInfo approveObjInfo) {
        if (approveObjInfo == null || approveObjInfo.getApproveObj() == null) {
            return null;
        }
        ApproveObjSignatureBO approveObjSignatureBO = new ApproveObjSignatureBO();
        approveObjSignatureBO.setSubmitter(approveObjInfo.getApproveObj().getSubmitter());
        if (CollectionUtil.isNotEmpty(approveObjInfo.getApproveObj().getCostCenters())) {
            approveObjSignatureBO.setCostCenterSignatureBOS(
                approveObjInfo.getApproveObj().getCostCenters().stream().filter(Objects::nonNull).map(costCenter -> {
                    CostCenterSignatureBO costCenterSignatureBO = new CostCenterSignatureBO();
                    costCenterSignatureBO.setCostCenterId(costCenter.getCostCenterId());
                    costCenterSignatureBO.setCostCenterName(costCenter.getCostCenterName());
                    costCenterSignatureBO.setCostLevel(costCenter.getCostLevel());
                    return costCenterSignatureBO;
                }).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(approveObjInfo.getApproveObj().getProjectNames())) {
            approveObjSignatureBO.setProjectNames(approveObjInfo.getApproveObj().getProjectNames());
        }
        if (CollectionUtil.isNotEmpty(approveObjInfo.getApproveObj().getTravelPurposeNames())) {
            approveObjSignatureBO.setTravelPurposeNames(approveObjInfo.getApproveObj().getTravelPurposeNames());
        }
        if (CollectionUtil.isNotEmpty(approveObjInfo.getApproveObj().getTravelers())) {
            approveObjSignatureBO.setTravelers(approveObjInfo.getApproveObj().getTravelers());
        }
        if (CollectionUtil.isNotEmpty(approveObjInfo.getApproveObj().getTravelerUids())) {
            approveObjSignatureBO.setTravelerUids(approveObjInfo.getApproveObj().getTravelerUids());
        }
        if (CollectionUtil.isNotEmpty(approveObjInfo.getApproveObj().getTravelerInfoList())) {
            approveObjSignatureBO.setTravelerInfoSignatureBOS(
                approveObjInfo.getApproveObj().getTravelerInfoList().stream().filter(Objects::nonNull)
                    .map(travelerInfo -> {
                        TravelerInfoSignatureBO travelerInfoSignatureBO = new TravelerInfoSignatureBO();
                        travelerInfoSignatureBO.setName(travelerInfo.getName());
                        travelerInfoSignatureBO.setUid(travelerInfo.getUid());
                        return travelerInfoSignatureBO;
                    }).collect(Collectors.toList()));
        }
        return approveObjSignatureBO;
    }

    /**
     * 构建审批流程
     *
     * @param rcInputList：客户选中的rc checkRcResult：agg管控计算后判断 提前rc、协议rc、低价rc是否有效
     * @return 客户预订流程选过的所有的有效rc
     */
    public static List<RCInput> buildValidRcList(List<RCInput> rcInputList, CheckRcResultType checkRcResult) {
        if (CollectionUtils.isEmpty(rcInputList)) {
            return Lists.newArrayList();
        }
        Predicate<RCInput> rcFilter = rcInput -> {
            if (rcInput == null || StringUtil.isBlank(rcInput.getRcToken())) {
                return false;
            }
            RcToken rcToken = TokenParseUtil.parseToken(rcInput.getRcToken(), RcToken.class);
            if (rcToken == null) {
                return false;
            }
            RcTypeEnum rcTypeEnum = RcTypeEnum.getType(rcToken.getType());
            if (rcTypeEnum == null) {
                return false;
            }
            if (OrderCreateProcessorOfUtil.checkRcInvalid(checkRcResult, rcTypeEnum)) {
                return true;
            }
            return false;
        };
        return rcInputList.stream().filter(rcFilter).collect(Collectors.toList());
    }

    /**
     * RC是否合法
     * 低价，协议，提前预定需要做是否需要的校验
     *
     * @return
     */
    private static boolean checkRcInvalid(CheckRcResultType checkRcResultType, RcTypeEnum rcTypeEnum) {
        // 管控接口返回的非法rc  非即已选的RC中，不再需要的RC OVER_STANDARD 超标RC BOOK_AHEAD 提前预定RC AGREEMENT 协议RC
        List<String> invalidRcList = Optional.ofNullable(checkRcResultType).map(CheckRcResultType::getInvalidRcTypeList)
            .orElse(Collections.emptyList());
        if (CollectionUtil.isEmpty(invalidRcList)) {
            return true;
        }
        if (!Arrays.asList(RcTypeEnum.LOW_PRICE, RcTypeEnum.AGREEMENT, RcTypeEnum.BOOK_AHEAD).contains(rcTypeEnum)) {
            return true;
        }
        if (RcTypeEnum.LOW_PRICE.equals(rcTypeEnum)) {
            return !invalidRcList.contains("OVER_STANDARD");
        }
        return !invalidRcList.contains(rcTypeEnum.getCode());
    }

    /**
     * errorCode 本项目占位699-601倒序排列 并且会返回给vo
     * logErrorCode 仅做日志记录 可赋值接口方响应code 接口未返回逻辑同 errorCode
     * 目前客户端针报错code都没啥特殊定制 如果未来有 需要使用 qConfigOfCodeMappingConfig 做映射的方式
     * errorMessage 接口方返回的错误信息
     * friendlyMessage 根据接口的code取shark的友好提示
     *
     * @param integrationResponse
     * @param orderCreateErrorEnum
     * @param serviceName          依赖的报错接口的服务名
     * @param actionName           依赖的报错接口的方法名
     * @return
     */
    public static ParamCheckResult buildParamCheckResult(IntegrationResponse integrationResponse,
        OrderCreateErrorEnum orderCreateErrorEnum, String serviceName, String actionName) {
        // Integer errorCode qconfig map的结果, String logErrorCode 接口的errorCode
        String logErrorCode = Optional.ofNullable(integrationResponse).map(IntegrationResponse::getErrorCode)
            .orElse(String.valueOf(orderCreateErrorEnum.getErrorCode()));
        String friendlyMessage = null;
        if (StringUtil.isNotBlank(serviceName) && StringUtil.isNotBlank(actionName)) {
            friendlyMessage = BFFSharkUtil.getSharkValue(
                StringUtil.indexedFormat(SoaErrorSharkKeyConstant.SOA_ERROR, serviceName, actionName,
                    String.valueOf(logErrorCode)));
        }
        return new ParamCheckResult(false, orderCreateErrorEnum.getErrorCode(), String.valueOf(logErrorCode),
            Optional.ofNullable(integrationResponse).map(IntegrationResponse::getErrorMessage).orElse(null),
            friendlyMessage);
    }

    /**
     * 是否需要变价
     *
     * @param createOrderResponseType
     * @return
     */
    public static boolean requirePriceChange(CreateOrderResponseType createOrderResponseType,
        OrderCreateToken orderCreateToken) {
        if (orderCreateToken.containsContinueType(ContinueTypeConst.PRICE_CHANGE)) {
            return false;
        }
        if (createOrderResponseType == null || createOrderResponseType.getOrderPaymentInfo() == null
            || createOrderResponseType.getOrderPaymentInfo().getAmountDifferenceInfo() == null) {
            return false;
        }
        if (!BooleanUtil.isTrue(
            createOrderResponseType.getOrderPaymentInfo().getAmountDifferenceInfo().isPriceFall())) {
            return false;
        }
        return MathUtils.isGreaterThanZero(
            createOrderResponseType.getOrderPaymentInfo().getAmountDifferenceInfo().getAmount());
    }

    /**
     * 是否需要确认订单
     *
     * @return
     */
    public static boolean requireConfirmOrder(OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateToken.containsContinueType(ContinueTypeConst.CONFIRM_ORDER)) {
            return false;
        }
        if (!StrategyOfBookingInitUtil.confirmOrder(orderCreateRequestType.getStrategyInfos())) {
            return false;
        }
        return true;
    }

    protected static CreateOrderResult buildCreateOrderResultByCreateOrder(
        CreateOrderResponseType createOrderResponseType, OrderCreateRequestType orderCreateRequestType) {
        CreateOrderResult createOrderResult = new CreateOrderResult();
        // 发布兼容仅对比了创单入参不真实发起调用
        if (orderCreateRequestType == null || createOrderResponseType == null) {
            return createOrderResult;
        }
        createOrderResult.setOrderID(OrderCreateProcessorOfUtil.buildOrderId(null, createOrderResponseType));
        if (Optional.ofNullable(createOrderResponseType.getOrderPaymentInfo()).map(OrderPaymentType::getOrderAmountInfo)
            .map(OrderAmountType::getPersonalPayAmountInfo).orElse(null) != null) {
            PayAmountResult payAmountResult = new PayAmountResult();
            AmountInfoType personalPayAmountInfo =
                createOrderResponseType.getOrderPaymentInfo().getOrderAmountInfo().getPersonalPayAmountInfo();
            payAmountResult.setAmount(personalPayAmountInfo.getAmount());
            payAmountResult.setCurrency(personalPayAmountInfo.getCurrency());
            payAmountResult.setExchangeRateToCNY(personalPayAmountInfo.getExchangeRateToCNY());
            createOrderResult.setPayAmountResult(payAmountResult);
        }
        if (CollectionUtil.isNotEmpty(createOrderResponseType.getInsuranceRoutAmount())) {
            createOrderResult.setInsuranceRoutAmountResults(
                createOrderResponseType.getInsuranceRoutAmount().stream().filter(Objects::nonNull)
                    .map(insuranceRoutAmount -> {
                        InsuranceRoutAmountResult insuranceRoutAmountResult = new InsuranceRoutAmountResult();
                        insuranceRoutAmountResult.setRoutType(insuranceRoutAmount.getRoutType());
                        insuranceRoutAmountResult.setTotalAmount(insuranceRoutAmount.getTotalAmount());
                        insuranceRoutAmountResult.setBeneficialCode(insuranceRoutAmount.getBeneficialCode());
                        return insuranceRoutAmountResult;
                    }).collect(Collectors.toList()));
        }
        if (createOrderResponseType.getOrderPaymentInfo() != null
            && createOrderResponseType.getOrderPaymentInfo().getOrderAmountInfo() != null && CollectionUtil.isNotEmpty(
            createOrderResponseType.getOrderPaymentInfo().getOrderAmountInfo().getPaymentItemList())) {
            createOrderResult.setPaymentItemTypeResults(
                createOrderResponseType.getOrderPaymentInfo().getOrderAmountInfo().getPaymentItemList().stream()
                    .filter(Objects::nonNull).map(paymentItemType -> {
                        PaymentItemTypeResult paymentItemTypeResult = new PaymentItemTypeResult();
                        paymentItemTypeResult.setFeeType(paymentItemType.getFeeType());
                        paymentItemTypeResult.setPrepayType(paymentItemType.getPrepayType());
                        if (paymentItemType.getAmountInfo() != null) {
                            PayAmountResult payAmountResult = new PayAmountResult();
                            payAmountResult.setCurrency(paymentItemType.getAmountInfo().getCurrency());
                            payAmountResult.setAmount(paymentItemType.getAmountInfo().getAmount());
                            payAmountResult.setExchangeRateToCNY(paymentItemType.getAmountInfo().getExchangeRateToCNY());
                            paymentItemTypeResult.setPayAmountResult(payAmountResult);
                        }
                        return paymentItemTypeResult;
                    }).collect(Collectors.toList()));
        }

        createOrderResult.setTransactionId(
            Optional.ofNullable(createOrderResponseType.getTransactionInfo()).map(TransactionInfo::getTransactionId)
                .orElse(null));
        createOrderResult.setPaymentExternalNo(createOrderResponseType.getPaymentExternalNo());
        createOrderResult.setTransactionUUID(getTransactionUUID(createOrderResponseType, orderCreateRequestType));
        createOrderResult.setPayLink(createOrderResponseType.getPayLink());
        createOrderResult.setPayToken(createOrderResponseType.getPayToken());
        return createOrderResult;
    }

    /**
     * 执行过创单流程还需要二次提交场景确认订单/变价需要保留订单号、支付信息等
     *
     * @param createOrderResponseType
     * @param orderCreateRequestType
     * @return
     */
    public static CreateOrderResult buildCreateOrderResult(CreateOrderResponseType createOrderResponseType,
        OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken) {
        if (!OrderCreateProcessorOfUtil.requireCreateOrder(orderCreateToken)
            && orderCreateToken.getCreateOrderResult() != null) {
            return orderCreateToken.getCreateOrderResult();
        }
        return buildCreateOrderResultByCreateOrder(createOrderResponseType, orderCreateRequestType);
    }

    protected static String getTransactionUUID(CreateOrderResponseType createOrderResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomPayType == HotelPayTypeEnum.UNION_PAY) {
            return Optional.ofNullable(createOrderResponseType.getTransactionInfo())
                .map(TransactionInfo::getSubTransactionIdList).orElse(new ArrayList<>()).stream().filter(
                    subTransactionInfoTO -> CREATE_ORDER_PAYTYPE.equalsIgnoreCase(subTransactionInfoTO.getPayType()))
                .findFirst().map(SubTransactionInfo::getSubTransactionId).orElse("");
        }
        return Optional.ofNullable(createOrderResponseType.getTransactionInfo())
            .map(TransactionInfo::getSubTransactionIdList).orElse(new ArrayList<>()).stream()
            .filter(subTransactionInfoTO -> ACCNT.equalsIgnoreCase(subTransactionInfoTO.getPayType())).findFirst()
            .map(SubTransactionInfo::getSubTransactionId).orElse("");
    }

    public static long buildOrderId(OrderCreateToken orderCreateToken,
        CreateOrderResponseType createOrderResponseType) {
        if (!OrderCreateProcessorOfUtil.requireCreateOrder(orderCreateToken)
            && orderCreateToken.getCreateOrderResult() != null) {
            return orderCreateToken.getCreateOrderResult().getOrderID();
        }
        return Optional.ofNullable(createOrderResponseType).map(CreateOrderResponseType::getOrderId).orElse(0L);
    }

    public static boolean buildOnlyServiceSelfPay(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken) {
        HotelPayTypeEnum roomType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        HotelPayTypeEnum serviceFeePayType =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        if (roomType == HotelPayTypeEnum.CASH && serviceFeePayType == HotelPayTypeEnum.SELF_PAY) {
            return true;
        }
        return false;
    }

    /**
     * 订单支付币种
     *
     * @return
     */
    public static String buildOrderCurrency(CreateOrderResponseType createOrderResponseType,
        OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        return Optional.ofNullable(createOrderResult).map(CreateOrderResult::getPayAmountResult)
            .map(PayAmountResult::getCurrency).orElse(null);
    }

    /**
     * 是否需要客户自行支付权限校验
     *
     * @return
     */
    public static boolean requireOrderOperation(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        return orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Offline
            && TemplateNumberUtil.isNotZeroAndNull(createOrderResult.getOrderID());
    }

    public static ApprovalInfoType buildApprovalInfoType(
        ApprovalFlowComputeResponseType approvalFlowComputeResponseType,
        MatchApprovalFlowResponseType matchApprovalFlowResponseType, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        ApprovalInfoType approvalInfoType = new ApprovalInfoType();
        // 有延用 不走审批流
        if (followApproval(orderCreateRequestType, orderCreateToken)) {
            approvalInfoType.setApprovalExtensionOrderId(
                OrderCreateProcessorOfUtil.buildFollowOrderNo(orderCreateRequestType, orderCreateToken));
            return approvalInfoType;
        }
        // 无延用 走审批流 变价+审批流默认通过二次提交 创单时变价了,要从token拿出已有的审批流信息, 提示用户.
        if (Optional.ofNullable(orderCreateToken.getApprovalInfoResult()).map(ApprovalInfoResult::isApprovalAllDone)
            .orElse(false)) {
            approvalInfoType.setExternalId(orderCreateToken.getApprovalInfoResult().getExternalId());
            return approvalInfoType;
        }
        // 非变价+审批流默认通过
        boolean isApprovalAllDone = BooleanUtil.parseStr(Boolean.TRUE).equals(
            Optional.ofNullable(approvalFlowComputeResponseType)
                .map(ApprovalFlowComputeResponseType::getApprovalAllDone).orElse(null));
        if (isApprovalAllDone) {
            String externalId =
                Optional.ofNullable(matchApprovalFlowResponseType.getFlowTmplInfo()).map(FlowTmpl::getExternalId)
                    .orElse(null);
            approvalInfoType.setExternalId(externalId);
            return approvalInfoType;
        }
        // 非默认通过+审批流二次提交 前端会回传审批流提交信息
        ApprovalFlowInput approvalFlowInput = orderCreateRequestType.getApprovalFlowInput();
        if (approvalFlowInput != null) {
            return ApprovalFlowUtil.getApprovalInfo(approvalFlowInput.getApprovalFlowToken(),
                approvalFlowInput.getPassword(), buildApprovers(approvalFlowInput.getApprovers()));
        }
        return null;
    }

    private static boolean needApprovalExtensionOrderId(FollowApprovalInfoInput followApprovalInfoInput,
        OrderCreateToken orderCreateToken) {
        long authFromOrderId =
            Optional.ofNullable(followApprovalInfoInput).map(FollowApprovalInfoInput::getFollowOrderId)
                .map(NumberUtil::parseLong).orElse(0L);
        long followTripId = Optional.ofNullable(followApprovalInfoInput).map(FollowApprovalInfoInput::getFollowTripId)
            .map(NumberUtil::parseLong).orElse(0L);
        return authFromOrderId > 0 && followTripId <= 0;
    }

    private static List<ApproverOutEntity> buildApprovers(List<Approver> approverList) {
        if (CollectionUtil.isEmpty(approverList)) {
            return null;
        }
        return approverList.stream().filter(Objects::nonNull).map(approver -> {
            ApproverOutEntity approverOutEntity = new ApproverOutEntity();
            approverOutEntity.setLevel(approver.getLevel());
            approverOutEntity.setUid(approver.getUid());
            approverOutEntity.setByUser("T");
            return approverOutEntity;
        }).collect(Collectors.toList());
    }

    private static String getAuthFromOrderId(FollowApprovalInfoInput followApprovalInfoInput,
        OrderCreateToken orderCreateToken) {
        if (!needApprovalExtensionOrderId(followApprovalInfoInput, orderCreateToken)) {
            return null;
        }
        long authFromOrderId =
            Optional.ofNullable(followApprovalInfoInput).map(FollowApprovalInfoInput::getFollowOrderId)
                .map(NumberUtil::parseLong).orElse(0L);
        return authFromOrderId > 0 ? String.valueOf(authFromOrderId) : null;
    }

    public static boolean requireOrderPriceChange(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken, Map<String, StrategyInfo> strategyInfoMap) {
        if (StrategyOfOrderCreateUtil.disableOrderPriceChange(strategyInfoMap)) {
            return false;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.ORDER_PRICE_CHANGE)) {
            return false;
        }
        if (StrategyOfBookingInitUtil.modify(orderCreateRequestType.getStrategyInfos())) {
            return true;
        }
        if (StrategyOfBookingInitUtil.applyModify(orderCreateRequestType.getStrategyInfos())) {
            return true;
        }
        if (StrategyOfBookingInitUtil.copyOrder(orderCreateRequestType.getStrategyInfos())) {
            return true;
        }
        return false;
    }

    public static ParamCheckResult buildParamCheckResultCreateOrderError(
        CreateOrderResponseType createOrderResponseType, QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig) {
        Integer logErrorCode =
            Optional.ofNullable(createOrderResponseType).map(CreateOrderResponseType::getResponseCode)
                .orElse(OrderCreateErrorEnum.CREATE_ORDER_ERROR.getErrorCode());
        String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_HOTEL_BOOK_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_CREATE_ORDER, String.valueOf(logErrorCode));
        Integer errorCode = OrderCreateErrorEnum.CREATE_ORDER_ERROR.getErrorCode();
        throw BusinessExceptionBuilder.createAlertException(errorCode,
            Optional.ofNullable(createOrderResponseType).map(CreateOrderResponseType::getResponseDesc).orElse(null),
            friendlyMessage, logErrorCode.toString(),
            SoaErrorSharkKeyConstant.buildActionInfo(logErrorCode, qConfigOfCodeMappingConfig,
                CODE_MAPPING_CONFIG_CREATE_ORDER, null));
    }

    /**
     * 按人管控需要查询所有人的审批信息
     * 按单管控只需要查询主单的审批信息
     *
     * @param orderCreateRequestType
     * @param checkTravelPolicyResponseType
     * @return
     */
    public static List<ApprovalInput> buildApprovalInputs(OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (checkTravelPolicyResponseType == null) {
            return new ArrayList<>();
        }
        if (!"P".equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            return new ArrayList<>();
        }
        return orderCreateRequestType.getHotelBookPassengerInputs().stream()
            .map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getApprovalInput)
            .collect(Collectors.toList());
    }

    public static String buildSearchRange(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (checkTravelPolicyResponseType == null) {
            return null;
        }
        if ("P".equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            return "ALL";
        }
        return null;
    }

    public static boolean requireSyncTrip(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, CreateTripResponseType createTripResponseType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (!accountInfo.isPackageEnabled()) {
            return false;
        }
        // 新建的行程肯定需要关联
        if (TemplateNumberUtil.getValue(
            Optional.ofNullable(createTripResponseType).map(CreateTripResponseType::getTripId).orElse(null)) > 0) {
            if (accountInfo.isTravelApplyRequired(
                CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
                orderCreateRequestType.getCorpPayInfo())) {
                return StringUtil.isNotBlank(Optional.ofNullable(orderCreateRequestType.getApprovalInput())
                    .map(ApprovalInput::getMasterApprovalNo).orElse(null));
            }
            return false;
        }
        return false;
        /* 联调发现，pc前端自己调createTrip接口的时候，没有传入出差申请单号，创建的行程单有问题，服务端关联也没有意义，所以暂时与老逻辑保持一致，新建行程才关联，其余场景不多调接口
        // 沿用的行程不需要关联
        if (buildIsTripFollow(orderCreateRequestType, orderCreateToken)) {
            return false;
        }
        // 无行程号不需要关联
        if (TemplateNumberUtil.getValue(
            getTripId(accountInfo, orderCreateRequestType, createTripResponseType, orderCreateToken)) < 0) {
            return false;
        }
        // 有行程号且关联过出差申请单不需要关联
        if (StringUtil.isBlank(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getMasterApprovalNo)
                .orElse(null))) {
            return false;
        }
        if (StringUtil.isNotBlank(buildCorpTravelNo(getTripBookingInfosResponseType))) {
            return false;
        }
        // 有行程号且未关联过出差申请单需要关联
        return true;*/
    }

    /*public static String buildCorpTravelNo(GetTripBookingInfosResponseType getTripBookingInfosResponseType) {
        List<UnAllowedBookingInfoAdv> unAllowedBookingInfoAdvs = Optional.ofNullable(getTripBookingInfosResponseType)
            .map(GetTripBookingInfosResponseType::getUnAllowedBookingList).orElse(null);
        if (CollectionUtils.isEmpty(unAllowedBookingInfoAdvs)) {
            return null;
        }
        List<Long> corpTravelNos =
            unAllowedBookingInfoAdvs.stream().filter(Objects::nonNull).map(UnAllowedBookingInfoAdv::getCorpTravelNo)
                .collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(corpTravelNos)) {
            return null;
        }
        Long corpTravelNo = corpTravelNos.stream().findFirst().orElse(null);
        if (corpTravelNo == null || corpTravelNo <= 0) {
            return null;
        }
        return String.valueOf(corpTravelNo);
    }*/

    public static boolean needGetCommonPassenger(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.INFOID_REMIND,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return false;
        }
        if (StrategyOfBookingInitUtil.applyModify(orderCreateRequestType.getStrategyInfos())) {
            return false;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(p -> StringUtil.equalsIgnoreCase(
            Optional.ofNullable(p.getHotelPassengerInput()).map(HotelPassengerInput::getEmployee).orElse(null),
            BooleanUtil.parseStr(false)))) {
            if (orderCreateToken.containsContinueType(ContinueTypeConst.INFOID_REMIND)) {
                return false;
            }
            if (StrategyOfBookingInitUtil.modify(orderCreateRequestType.getStrategyInfos())) {
                return true;
            }
            if (StrategyOfBookingInitUtil.applyModify(orderCreateRequestType.getStrategyInfos())) {
                return true;
            }
            if (StrategyOfBookingInitUtil.copyOrder(orderCreateRequestType.getStrategyInfos())) {
                return true;
            }
            return true;
        }
        return false;
    }

    public static boolean requireMatchCostCenter(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, Boolean saveCommonDataCostCenter, ResourceToken resourceToken,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (needCostCenterNew(resourceToken, orderCreateRequestType.getCorpPayInfo(), accountInfo, strategyInfoMap)) {
            return false;
        }
        if (BooleanUtil.isTrue(saveCommonDataCostCenter)) {
            return false;
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (accountInfo.isPackageEnabled()) {
            return false;
        }
        if (StringUtil.isNotBlank(
            Optional.ofNullable(orderCreateRequestType.getMiceInput()).map(MiceInput::getMiceToken).orElse(null))) {
            return false;
        }
        return true;
    }

    public static boolean saveOrderCostCenter(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, Boolean saveCommonDataCostCenter, OrderCreateToken orderCreateToken,
        ResourceToken resourceToken, Map<String, StrategyInfo> strategyInfoMap,
        CreateOrderResponseType createOrderResponseType) {
        if (needCostCenterNew(resourceToken, orderCreateRequestType.getCorpPayInfo(), accountInfo, strategyInfoMap)) {
            return false;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() != PosEnum.CHINA) {
            return false;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.PRICE_CHANGE)) {
            return false;
        }
        if (!requireMatchCostCenter(orderCreateRequestType, accountInfo, saveCommonDataCostCenter, resourceToken,
            strategyInfoMap)) {
            return false;
        }
        if (buildOrderId(orderCreateToken, createOrderResponseType) <= 0) {
            return false;
        }
        return true;
    }

    public static CheckOverStandardRcInfoType getCheckOverStandardRcInfoType(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, List<HotelPayTypeInput> hotelPayTypeInputs,
        ResourceToken resourceToken) {
        if (CollectionUtils.isEmpty(
            Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getCheckRcResult)
                .map(CheckRcResultType::getOverStandardRc).orElse(null))) {
            return new CheckOverStandardRcInfoType();
        }
        String payType = getServiceChargePayType(
            HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs, resourceToken));
        return Optional.ofNullable(checkTravelPolicyResponseType.getCheckRcResult().getOverStandardRc())
            .orElse(new ArrayList<>()).stream()
            .filter(o -> StringUtil.compareIgnoreCase(o.getServiceChargeType(), payType)).findFirst()
            .orElse(new CheckOverStandardRcInfoType());
    }

    private static final String CHARGE_PAY_NONE = "NONE";

    private static final String CHARGE_PAY_PERSONAL = "PERSONAL_PAY_SERVICE_CHARGE";

    private static final String CHARGE_PAY_CORP = "CORP_PAY_SERVICE_CHARGE";

    private static String getServiceChargePayType(HotelPayTypeEnum servicePayType) {
        if (servicePayType == null) {
            return CHARGE_PAY_NONE;
        }
        switch (servicePayType) {
            case CORP_PAY:
                return CHARGE_PAY_CORP;
            case SELF_PAY:
                return CHARGE_PAY_PERSONAL;
            default:
                return CHARGE_PAY_NONE;
        }
    }

    /**
     * 创单前置校验
     *
     * @return
     */
    public static boolean requireCreateOrderCheck(OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomPayType == HotelPayTypeEnum.FLASH_STAY_PAY) {
            return true;
        }
        return false;
    }

    public static BookInfoBO buildBookInfoBO(HotelPayTypeEnum roomPayType, HotelPayTypeEnum servicePayType) {
        BookInfoBO bookInfoBO = new BookInfoBO();
        bookInfoBO.setRoomPayType(Optional.ofNullable(roomPayType).map(HotelPayTypeEnum::getCode).orElse(null));
        if (Arrays.asList(HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.SELF_PAY).contains(servicePayType)) {
            bookInfoBO.setServicePayType(
                Optional.ofNullable(servicePayType).map(HotelPayTypeEnum::getCode).orElse(null));
        }
        return bookInfoBO;
    }



    public static String buildSignatureBookInfo(BookInfoBO bookInfoBO) {
        if (bookInfoBO == null) {
            return StringUtilsExt.EMPTY;
        }
        String jsonStr = JsonUtil.toJson(bookInfoBO);
        // 记录下加签数据 方便问题查找
        LogUtil.loggingClogOnly(LogLevelEnum.Info, OrderCreateProcessorOfUtil.class, "buildSignatureBookInfo",
            jsonStr, null);
        // 选取关键元素做签名
        try {
            return MD5Utils.md5(jsonStr);
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, OrderCreateProcessorOfUtil.class, "buildSignatureBookInfo",
                jsonStr, null);
        }
        return null;
    }

    public static boolean requireRegister(OrderCreateToken orderCreateToken,
                                          RegisterInputInfo registerInputInfo,
                                          GetCorpUserHotelVipCardResponseType getCorpUserHotelVipCardResponseType,
                                          IntegrationSoaRequestType integrationSoaRequestType,
                                          WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo,
                                          QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        if (checkAvailContextInfo == null) {
            return true;
        }

        // 无注册信息
        if (registerInputInfo == null) {
            return false;
        }
        // 已经异步注册过了
        if (orderCreateToken != null && StringUtil.isNotBlank(orderCreateToken.getMembershipRegisterId())) {
            return false;
        }
        if (queryBizModeBindRelationResponseType == null) {
            return true;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(RequestHeaderUtil.getUserId(integrationSoaRequestType), t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return true;
        }
        // 是否已经有会员卡号
        if (getCorpUserHotelVipCardResponseType == null || CollectionUtil.isEmpty(getCorpUserHotelVipCardResponseType.getCorpUserHotelVipCardList())) {
            return true;
        }
        Boolean hasMembershipNo = getCorpUserHotelVipCardResponseType.getCorpUserHotelVipCardList().stream().filter(Objects::nonNull)
                .filter(t -> StringUtil.equalsIgnoreCase(t.getUid(), bizModeBindRelationData.getPrimaryDimensionId()))
                .filter(z -> z.getHotelGroupID() != null && checkAvailContextInfo.getGroupId() == z.getHotelGroupID())
                .anyMatch(x -> StringUtil.isNotBlank(x.getHtlVipCardID()));
        return BooleanUtil.isNotTrue(hasMembershipNo);

    }

    public static boolean buildMultiCurrency(String currency) {
        return !equalsIgnoreCase(currency, RMB) && !equalsIgnoreCase(currency, CNY);
    }

    /**
     * 是否需要查询每个入住人的账户配置信息
     * 提前审批策略按出行人 & 存在员工
     * 需要查询对应入住人的提前审批的配置
     *
     * @param accountInfo
     * @param orderCreateRequestType
     * @return
     */
    public static boolean requirePsgAccountInfo(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (accountInfo == null || orderCreateRequestType == null) {
            return false;
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (!accountInfo.preApprovalStrategyByPsg(
            CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            return false;
        }
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream()
            .anyMatch(OrderCreateProcessorOfUtil::buildEmployee)) {
            return true;
        }
        return false;
    }

    /**
     * 是否员工
     *
     * @param hotelBookPassengerInput
     * @return
     */
    public static boolean buildEmployee(HotelBookPassengerInput hotelBookPassengerInput) {
        return BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(hotelBookPassengerInput).map(HotelBookPassengerInput::getHotelPassengerInput)
                .map(HotelPassengerInput::getEmployee).orElse(null));
    }

    public static void checkSaveCommonData(SaveCommonDataResponseType saveCommonDataResponseType) {
        if (Optional.ofNullable(saveCommonDataResponseType).map(SaveCommonDataResponseType::getResponseCode).orElse(0)
            .equals(CommonConstant.SUCCESS_20000)) {
            return;
        }
        Integer errorCode = OrderCreateErrorEnum.SAVE_COMMON_DATA_ERROR.getErrorCode();
        Integer logErrorCode =
            Optional.ofNullable(saveCommonDataResponseType).map(SaveCommonDataResponseType::getResponseCode)
                .orElse(OrderCreateErrorEnum.SAVE_COMMON_DATA_ERROR.getErrorCode());
        String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageHasDefault(
            SoaErrorSharkKeyConstant.SERVICE_NAME_ORDER_FOUNDATION_CENTER_DATA_SYNC_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_SAVE_COMMON_DATA, String.valueOf(logErrorCode));
        throw BusinessExceptionBuilder.createAlertException(errorCode,
            Optional.ofNullable(saveCommonDataResponseType).map(SaveCommonDataResponseType::getResponseDesc)
                .orElse(null), friendlyMessage, String.valueOf(logErrorCode));
    }

    public static boolean needPolicyUidUserInfo(HotelPolicyInput hotelPolicyInput) {
        if (hotelPolicyInput == null) {
            return false;
        }
        if (hotelPolicyInput.getPolicyInput() == null) {
            return false;
        }
        return StringUtil.isNotBlank(hotelPolicyInput.getPolicyInput().getPolicyUid());
    }

    public static boolean needRetrieveTicketsByOrder(CorpPayInfo corpPayInfo,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (CorpPayInfoUtil.isPublic(corpPayInfo)) {
            return false;
        }
        if (StrategyOfBookingInitUtil.blockRetrieveTicket(strategyInfoMap)) {
            return false;
        }
        return true;
    }
    public static boolean needQueryBizModeBindRelation(IntegrationSoaRequestType integrationSoaRequestType,
        MembershipInfo membershipInfo) {
        List<String> uids = buildUids(integrationSoaRequestType, membershipInfo);
        if (CollectionUtil.isEmpty(uids)) {
            return false;
        }
        return true;
    }

    public static List<String> buildUids(IntegrationSoaRequestType integrationSoaRequestType,
        MembershipInfo membershipInfo) {
        Set<String> res = new HashSet<>();
        String uid = Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
            .map(UserInfo::getUserId).orElse(null);
        if (StringUtils.isNotBlank(uid)) {
            res.add(uid);
        }
        String memberUid = Optional.ofNullable(membershipInfo).map(MembershipInfo::getMembershipUid).orElse(null);
        if (StringUtils.isNotBlank(memberUid)) {
            res.add(memberUid);
        }
        return res.stream().toList();
    }

    public static boolean needGetPlatformRelationByUid(IntegrationSoaRequestType integrationSoaRequestType,
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        if (queryBizModeBindRelationResponseType == null) {
            return false;
        }
        String uid = Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
            .map(UserInfo::getUserId).orElse(null);
        BizModeBindRelationData bizModeBindRelationData =
            CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(uid, t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return false;
        }
        return true;
    }

    public static boolean needQueryHotelOrderData(ResourceToken resourceToken) {
        return TemplateNumberUtil.isNotZeroAndNull(
            (Optional.ofNullable(resourceToken.getOrderResourceToken()).map(OrderResourceToken::getOrderId)
                .orElse(0L)));
    }

    public static boolean needTripDetail(TripInput tripInput) {
        return Long.parseLong(Optional.ofNullable(tripInput).map(TripInput::getTripId).orElse("0")) > 0;
    }

    public static boolean needTripDetailFollow(OrderCreateToken orderCreateToken) {
        if (orderCreateToken == null || orderCreateToken.getFollowApprovalResult() == null) {
            return false;
        }
        return TemplateNumberUtil.getValue(TemplateNumberUtil.parseLong(
            Optional.ofNullable(orderCreateToken.getFollowApprovalResult()).map(FollowApprovalResult::getTripId)
                .orElse(null))) > 0;
    }

    public static boolean needGetPolicyUsers(PolicyInput policyInput,
        IntegrationSoaRequestType integrationSoaRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        if (policyInput == null || StringUtil.isBlank(policyInput.getPolicyUid()) || accountInfo == null) {
            return false;
        }
        // 非 行程+政策执行人
        if (!accountInfo.isPackageEnabled() || !accountInfo.isPolicyModel()) {
            return false;
        }
        // 政策执行人是自己
        if (StringUtil.equalsIgnoreCase(integrationSoaRequestType.getUserInfo().getUserId(),
            policyInput.getPolicyUid())) {
            return false;
        }
        return true;
    }

    public static boolean needGetUserServedCardInfo(PolicyInput policyInput,
        IntegrationSoaRequestType integrationSoaRequestType, WrapperOfAccount.AccountInfo accountInfo,
        CorpPayInfo corpPayInfo, CityInput cityInfo) {
        // 无政策执行人
        if (policyInput == null || StringUtil.isBlank(policyInput.getPolicyUid())) {
            return false;
        }
        // 无登录卡信息
        if (integrationSoaRequestType == null || integrationSoaRequestType.getUserInfo() == null || StringUtil.isBlank(
            integrationSoaRequestType.getUserInfo().getUserId())) {
            return false;
        }
        if (cityInfo == null || accountInfo == null) {
            return false;
        }
        // 行程+政策执行人
        if (accountInfo.isPackageEnabled() && accountInfo.isPolicyModel()) {
            return false;
        }
        // 同行程
        if (accountInfo.bookPolicyPsgMustSameTripApprove(CityInfoUtil.oversea(cityInfo.getCityId()), corpPayInfo)) {
            return false;
        }
        // 非政策执行人
        if (!accountInfo.isPolicyModel()) {
            return false;
        }
        // 政策执行人是自己
        if (StringUtil.equalsIgnoreCase(integrationSoaRequestType.getUserInfo().getUserId(),
            policyInput.getPolicyUid())) {
            return false;
        }
        return true;
    }
    public static boolean needCustomConfigSearch(CorpPayInfo corpPayInfo,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType.getSourceFrom() != SourceFrom.Offline) {
            return false;
        }
        return CorpPayInfoUtil.isPublic(corpPayInfo);
    }

    public static boolean needExternalEmployeeId(CustomConfigSearchResponseType customConfigSearchResponseType,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType.getSourceFrom() != SourceFrom.Offline) {
            return false;
        }
        if (customConfigSearchResponseType == null || customConfigSearchResponseType.getCustomConfig() == null) {
            return false;
        }
        if (!TENCENT_OFFLINE.equalsIgnoreCase(customConfigSearchResponseType.getCustomConfig().getConfigType())) {
            return false;
        }
        if (CollectionUtil.isEmpty(customConfigSearchResponseType.getCustomConfig().getConfigResult())) {
            return false;
        }
        String valueOfInputExternalEid = customConfigSearchResponseType.getCustomConfig().getConfigResult()
            .get(CustomConfigTypeEnum.INPUT_EXTERNAL_EID.name());
        return OPEN.equalsIgnoreCase(valueOfInputExternalEid);
    }


    public static boolean needCheckCostCenterNew(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken, WrapperOfAccount.AccountInfo accountInfo,
        Map<String, StrategyInfo> strategyInfoMap, SearchTripDetailResponseType searchTripDetailResponseType,
        SearchTripDetailResponseType flowSearchTripDetailResponseType, OrderCreateToken orderCreateToken) {
        // 新版成本中心2.0
        if (needCostCenterNew(resourceToken, orderCreateRequestType.getCorpPayInfo(), accountInfo, strategyInfoMap)) {
            return needCostCenter(searchTripDetailResponseType, flowSearchTripDetailResponseType,
                orderCreateRequestType, orderCreateToken, accountInfo, strategyInfoMap);
        }
        // 蓝色空间老版
        return CollectionUtil.isNotEmpty(orderCreateRequestType.getCostCenterInputs());
    }

    /**
     * 是否需要成本中心
     * 因私不需要
     * 因公+单订单 需要
     * 因公+行程打包单+传入策略补单成本中心策略（蓝色空间）+且补单场景
     *
     * @param accountInfo
     * @return
     */
    public static boolean needCostCenter(SearchTripDetailResponseType searchTripDetailResponseType,
        SearchTripDetailResponseType flowSearchTripDetailResponseType, OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken, WrapperOfAccount.AccountInfo accountInfo,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (!accountInfo.isPackageEnabled()) {
            return true;
        }
        if (!StrategyOfBookingInitUtil.tripNeedCostCenter(strategyInfoMap)) {
            return false;
        }
        // 行程补单需要匹配审批流 需要加载成本中心
        return requireApprovalFlowMatchOfTrip(searchTripDetailResponseType, flowSearchTripDetailResponseType,
            orderCreateRequestType, orderCreateToken);
    }

    /**
     * 新版成本中心2.0
     * @param resourceToken
     * @param corpPayInfo
     * @param accountInfo
     * @param strategyInfoMap
     * @return
     */
    public static boolean needCostCenterNew(ResourceToken resourceToken, CorpPayInfo corpPayInfo,
        WrapperOfAccount.AccountInfo accountInfo, Map<String, StrategyInfo> strategyInfoMap) {
        if (CorpPayInfoUtil.isPrivate(corpPayInfo)) {
            return false;
        }
        if (accountInfo.isPackageEnabled()) {
            if (StrategyOfBookingInitUtil.tripNeedCostCenter(strategyInfoMap)) {
                return BooleanUtil.parseStr(true).equalsIgnoreCase(
                    Optional.ofNullable(resourceToken).map(ResourceToken::getBookInitResourceToken)
                        .map(BookInitResourceToken::getCostCenterNewUse).orElse(null));
            }
            return false;
        }
        return BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(resourceToken).map(ResourceToken::getBookInitResourceToken)
                .map(BookInitResourceToken::getCostCenterNewUse).orElse(null));
    }

    public static List<CheckCostCenterPassenger> buildCheckCostCenterPassengers(
        List<HotelBookPassengerInput> hotelBookPassengerInputs, CityInput cityInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        List<CheckCostCenterPassenger> checkCostCenterPassengers = new ArrayList<>();
        hotelBookPassengerInputs.forEach(hotelBookPassengerInput -> {
            CheckCostCenterPassenger checkCostCenterPassenger = new CheckCostCenterPassenger();
            checkCostCenterPassenger.setName(getUseName(hotelBookPassengerInput, cityInput.getCityId(), checkAvailInfo,
                qconfigOfCertificateInitConfig, strategyInfoMap));
            checkCostCenterPassenger.setUid(
                StringUtil.isNotBlank(hotelBookPassengerInput.getHotelPassengerInput().getUid()) ?
                    hotelBookPassengerInput.getHotelPassengerInput().getUid() :
                    hotelBookPassengerInput.getHotelPassengerInput().getInfoId());
            checkCostCenterPassenger.setCorpUser(hotelBookPassengerInput.getHotelPassengerInput().getEmployee());
            checkCostCenterPassenger.setTemporaryId(hotelBookPassengerInput.getHotelPassengerInput().getTemporaryId());
            checkCostCenterPassengers.add(checkCostCenterPassenger);
        });
        return checkCostCenterPassengers;
    }

    /**
     * 成本中心名称和订单创建请求中的乘客名称对比
     *
     * @param saveCommonDataRequestType
     * @param createOrderRequestType
     */
    public static List<String> savePassengerName(SaveCommonDataRequestType saveCommonDataRequestType,
        CreateOrderRequestType createOrderRequestType, ResourceToken resourceToken,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
        Map<String, StrategyInfo> strategyInfoMap, SearchTripDetailResponseType searchTripDetailResponseType,
        SearchTripDetailResponseType flowSearchTripDetailResponseType, OrderCreateToken orderCreateToken) {
        if (!needCompareSavePassengerName(resourceToken, orderCreateRequestType, accountInfo, strategyInfoMap,
            searchTripDetailResponseType, flowSearchTripDetailResponseType, orderCreateToken)) {
            return null;
        }
        // 老成本中心-蓝色空间-不存在按人的成本中心的时候会记录
        if (CollectionUtil.isEmpty(
            Optional.ofNullable(saveCommonDataRequestType).map(SaveCommonDataRequestType::getCostCenter)
                .map(CostCenterInfoType::getPassengerCostCenterList).orElse(null))) {
            LogUtil.logCompare("savePassengerName-saveCommonData-psgNull", JsonUtil.toJson(saveCommonDataRequestType),
                JsonUtil.toJson(createOrderRequestType));
            return null;
        }
        // 应该不存在入住人是空的情况
        if (CollectionUtil.isEmpty(
            Optional.ofNullable(createOrderRequestType).map(CreateOrderRequestType::getClientList).orElse(null))) {
            LogUtil.logCompare("savePassengerName-createOrder-psgNull", JsonUtil.toJson(saveCommonDataRequestType),
                JsonUtil.toJson(createOrderRequestType));
            return null;
        }
        List<String> difference = new ArrayList<>();
        // 有uid和infoid的 具体对比到个人
        createOrderRequestType.getClientList().stream().forEach(clientEntity -> {
            PassengerCostCenterInfoType passengerCostCenterInfo =
                saveCommonDataRequestType.getCostCenter().getPassengerCostCenterList().stream()
                    .filter(passengerCostCenterInfoType -> samePassenger(clientEntity, passengerCostCenterInfoType))
                    .toList().stream().findFirst().orElse(null);
            if (passengerCostCenterInfo == null) {
                return;
            }
            if (StringUtil.equalsIgnoreCase(passengerCostCenterInfo.getPassengerName(), clientEntity.getName())) {
                return;
            }
            difference.add(clientEntity.getName());
            LogUtil.logCompare("savePassengerName", JsonUtil.toJson(saveCommonDataRequestType),
                JsonUtil.toJson(createOrderRequestType));
        });
        if (CollectionUtil.isNotEmpty(difference)) {
            return difference;
        }
        // 蓝色空间有不存在infoid和uid的 先整体对比一遍名称
        List<String> passengerNameListCreateOrder =
            createOrderRequestType.getClientList().stream().map(ClientEntity::getName).filter(StringUtil::isNotBlank)
                .toList();
        List<String> passengerNameListSaveCommonData =
            saveCommonDataRequestType.getCostCenter().getPassengerCostCenterList().stream()
                .map(PassengerCostCenterInfoType::getPassengerName).filter(StringUtil::isNotBlank).toList();
        if (CollectionUtil.isEmpty(passengerNameListCreateOrder) || CollectionUtil.isEmpty(
            passengerNameListSaveCommonData)) {
            LogUtil.logCompare("savePassengerName-saveCommonData-miss", JsonUtil.toJson(saveCommonDataRequestType),
                JsonUtil.toJson(createOrderRequestType));
            return null;
        }
        passengerNameListCreateOrder.stream().forEach(clientName -> {
            // 只要有一个出行人名称不一致就记录
            if (passengerNameListSaveCommonData.stream()
                .noneMatch(passengerName -> StringUtil.equalsIgnoreCase(passengerName, clientName))) {
                difference.add(clientName);
                LogUtil.logCompare("savePassengerName-saveCommonData-miss",
                    JsonUtil.toJson(saveCommonDataRequestType), JsonUtil.toJson(createOrderRequestType));
                return;
            }
        });
        passengerNameListSaveCommonData.stream().forEach(passengerName -> {
            // 只要有一个成本中心出行人名称不一致就记录
            if (passengerNameListCreateOrder.stream()
                .noneMatch(clientName -> StringUtil.equalsIgnoreCase(clientName, passengerName))) {
                difference.add(passengerName);
                LogUtil.logCompare("savePassengerName-createOrder-miss", JsonUtil.toJson(saveCommonDataRequestType),
                    JsonUtil.toJson(createOrderRequestType));
                return;
            }
        });
        return difference;
    }

    protected static boolean samePassenger(ClientEntity clientEntity,
        PassengerCostCenterInfoType passengerCostCenterInfoType) {
        if (clientEntity == null || passengerCostCenterInfoType == null) {
            return false;
        }
        if (StringUtil.isNotBlank(clientEntity.getUid()) && StringUtil.isNotBlank(
            passengerCostCenterInfoType.getPassengerUid())) {
            return StringUtil.equalsIgnoreCase(clientEntity.getUid(), passengerCostCenterInfoType.getPassengerUid());
        }
        if (TemplateNumberUtil.getValue(clientEntity.getInfoId()) > 0 && clientEntity.getInfoId()
            .equals(TemplateNumberUtil.parseInt(passengerCostCenterInfoType.getInfoId()))) {
            return true;
        }
        return false;
    }

    /**
     * 老成本中心：国内站通过saveordercostcenter落地，无需参与对比，人名逻辑用的收口的OrderCreateProcessorOfUtil.getUseName
     * 老成本中心：蓝色空间44233和集成服务都是按人走的时候才会传入成本出行人人名，所以此处参与对比
     * 新版成本中心：不论是否按人走，均会传入出行人，参与对比
     */
    protected static boolean needCompareSavePassengerName(ResourceToken resourceToken,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
        Map<String, StrategyInfo> strategyInfoMap, SearchTripDetailResponseType searchTripDetailResponseType,
        SearchTripDetailResponseType flowSearchTripDetailResponseType, OrderCreateToken orderCreateToken) {
        if (OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, orderCreateRequestType.getCorpPayInfo(),
            accountInfo, strategyInfoMap)) {
            if (needCostCenter(searchTripDetailResponseType, flowSearchTripDetailResponseType,
                orderCreateRequestType, orderCreateToken, accountInfo, strategyInfoMap)) {
                return true;
            }
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() == PosEnum.CHINA) {
            return false;
        }
        return true;
    }

    public static boolean needCheckHotelAuthExtensionOfAiContinue(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType, OrderCreateToken orderCreateToken) {
        if (orderCreateRequestType.getContinueInfo() == null) {
            return false;
        }
        if (!ContinueTypeConst.APPROVAL_FLOW_REUSE.equalsIgnoreCase(
            orderCreateRequestType.getContinueInfo().getContinueCode())) {
            return false;
        }
        if (!ApprovalFlowReuseConstant.REUSE.equalsIgnoreCase(
            orderCreateRequestType.getContinueInfo().getContinueCode())) {
            return false;
        }
        if (orderCreateToken.getFollowApprovalResult() == null) {
            return false;
        }
        if (StringUtil.isBlank(orderCreateToken.getFollowApprovalResult().getFollowOrderNo())) {
            return false;
        }
        // 重新预定
        if (TemplateNumberUtil.isNotZeroAndNull(
            buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap))) {
            return false;
        }
        // 人工沿用单号
        if (TemplateNumberUtil.isNotZeroAndNull(
            buildArtificialReuseNoOrderId(orderCreateRequestType, getOrderFoundationDataResponseType))) {
            return false;
        }
        // 人工沿用单号-行程号
        if (TemplateNumberUtil.isNotZeroAndNull(
            buildArtificialReuseNoTripId(orderCreateRequestType, getOrderFoundationDataResponseType))) {
            return false;
        }
        // 智能推荐二次提交
        return true;
    }

    public static boolean needCheckHotelAuthExtension(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType, OrderCreateToken orderCreateToken) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (NOT_REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            return false;
        }
        // 智能二次提交再次校验
        if (orderCreateToken.containsContinueType(ContinueTypeConst.APPROVAL_FLOW_REUSE)) {
            return needCheckHotelAuthExtensionOfAiContinue(orderCreateRequestType, strategyInfoMap,
                getOrderFoundationDataResponseType, orderCreateToken);
        }
        if (TemplateNumberUtil.isNotZeroAndNull(
            buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap))) {
            return true;
        }
        if (TemplateNumberUtil.isNotZeroAndNull(
            buildArtificialReuseNoOrderId(orderCreateRequestType, getOrderFoundationDataResponseType))) {
            return true;
        }
        if (TemplateNumberUtil.isNotZeroAndNull(
            buildArtificialReuseNoTripId(orderCreateRequestType, getOrderFoundationDataResponseType))) {
            return true;
        }
        return false;
    }

    public static boolean needQueryHotelAuthExtension(OrderCreateRequestType orderCreateRequestType,
        GetAuthDelayResponseType getAuthDelayResponseType, OrderCreateToken orderCreateToken,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (StrategyOfBookingInitUtil.approvalReuseReBook(strategyInfoMap)) {
            return false;
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return false;
        }
        if (NOT_REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            return false;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.APPROVAL_FLOW_REUSE)) {
            return false;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Offline) {
            if (Optional.ofNullable(getAuthDelayResponseType).map(GetAuthDelayResponseType::getHotelAuthDelayconfig)
                .map(HotelAuthDelayConfig::isSmartauthdelayCf).orElse(false)) {
                return true;
            }
            return false;
        }
        if (Optional.ofNullable(getAuthDelayResponseType).map(GetAuthDelayResponseType::getHotelAuthDelayconfig)
            .map(HotelAuthDelayConfig::isSmartauthdelayCo).orElse(false)) {
            return true;
        }
        return false;
    }

    public static boolean needSearchTripBasicInfoOfApprovalFlowReuse(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        Long tripId =
            searchTripIdOfApprovalFlowReuse(checkHotelAuthExtensionResponseType, queryHotelAuthExtensionResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(tripId)) {
            return true;
        }
        return false;
    }

    public static Long searchTripIdOfApprovalFlowReuse(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        Long checkTripId =
            Optional.ofNullable(checkHotelAuthExtensionResponseType).map(CheckHotelAuthExtensionResponseType::getTripId)
                .orElse(null);
        if (TemplateNumberUtil.isNotZeroAndNull(checkTripId)) {
            return checkTripId;
        }
        Long queryTripId = Optional.ofNullable(queryHotelAuthExtensionResponseType)
            .map(QueryHotelAuthExtensionResponseType::getOriginalOrderInfo).map(OriginalOrderInfoType::getTripId)
            .orElse(null);
        if (TemplateNumberUtil.isNotZeroAndNull(queryTripId)) {
            return queryTripId;
        }
        return null;
    }

    public static boolean needOrderDetailOfApprovalFlowReuse(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        if (NOT_REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            return false;
        }
        Long orderId =
            orderIdOfApprovalFlowReuse(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType,
                queryHotelAuthExtensionResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(orderId)) {
            return true;
        }
        return false;
    }

    public static boolean needPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        Long orderId =
            orderIdOfApprovalFlowReuse(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType,
                queryHotelAuthExtensionResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(orderId)) {
            return true;
        }
        return false;
    }

    public static Long orderIdOfApprovalFlowReuse(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        Long approvalReuseReBookOrderId = buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap);
        if (TemplateNumberUtil.isNotZeroAndNull(approvalReuseReBookOrderId)) {
            return approvalReuseReBookOrderId;
        }
        Long artificialReuseNoOrderId =
            buildArtificialReuseNoOrderId(orderCreateRequestType, getOrderFoundationDataResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(artificialReuseNoOrderId)) {
            return artificialReuseNoOrderId;
        }
        Long queryOrderId = Optional.ofNullable(queryHotelAuthExtensionResponseType)
            .map(QueryHotelAuthExtensionResponseType::getOriginalOrderInfo)
            .map(OriginalOrderInfoType::getOriginalOrderId).orElse(null);
        if (TemplateNumberUtil.isNotZeroAndNull(queryOrderId)) {
            return queryOrderId;
        }
        return null;
    }

    /**
     * 人工沿用单号-订单号
     *
     * @return
     */
    public static Long buildArtificialReuseNoOrderId(OrderCreateRequestType orderCreateRequestType,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType) {
        if (StringUtil.isBlank(Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
            .map(ApprovalFlowReuseInput::getArtificialReuseNo).orElse(null))) {
            return null;
        }
        if (PRODUCT_LINE_TRIP == Optional.ofNullable(getOrderFoundationDataResponseType)
            .map(GetOrderFoundationDataResponseType::getOrderFoundationDataInfo)
            .map(OrderFoundationDataInfo::getProductLine).orElse(0)) {
            return null;
        }
        return TemplateNumberUtil.parseLong(Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
            .map(ApprovalFlowReuseInput::getArtificialReuseNo).orElse(null));
    }

    /**
     * 人工沿用单号-行程号
     *
     * @return
     */
    public static Long buildArtificialReuseNoTripId(OrderCreateRequestType orderCreateRequestType,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType) {
        if (StringUtil.isBlank(Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
            .map(ApprovalFlowReuseInput::getArtificialReuseNo).orElse(null))) {
            return null;
        }

        if (PRODUCT_LINE_TRIP != Optional.ofNullable(getOrderFoundationDataResponseType)
            .map(GetOrderFoundationDataResponseType::getOrderFoundationDataInfo)
            .map(OrderFoundationDataInfo::getProductLine).orElse(0)) {
            return null;
        }
        return TemplateNumberUtil.parseLong(Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
            .map(ApprovalFlowReuseInput::getArtificialReuseNo).orElse(null));
    }

    /**
     * 智能推荐沿单号
     *
     * @return
     */
    public static Long buildApprovalReuseAiBookOrderId(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        return Optional.ofNullable(queryHotelAuthExtensionResponseType)
            .map(QueryHotelAuthExtensionResponseType::getOriginalOrderInfo)
            .map(OriginalOrderInfoType::getOriginalOrderId).orElse(null);
    }

    /**
     * 非智能审批沿用原单号
     *
     * @return
     */
    public static Long buildApprovalReuseReBookOrderId(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (!StrategyOfBookingInitUtil.approvalReuseReBook(strategyInfoMap)) {
            return null;
        }
        return TemplateNumberUtil.parseLong(Optional.ofNullable(orderCreateRequestType.getOriginalOrderInput())
            .map(OriginalOrderInput::getOriginalOrderId).orElse(null));
    }

    public static boolean needGetOrderFoundationData(OrderCreateRequestType orderCreateRequestType) {
        if (NOT_REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            return false;
        }
        if (StringUtil.isBlank(Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
            .map(ApprovalFlowReuseInput::getArtificialReuseNo).orElse(null))) {
            return false;
        }
        return true;
    }

    /**
     * 审批沿用灰度
     *
     * @return
     */
    public static boolean approvalFlowReuseNew(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap, OrderCreateToken orderCreateToken) {
        // 重新预定-vo控制，传入新节点代表肯定是灰度开启了
        if (StringUtil.isBlank(Optional.ofNullable(orderCreateRequestType.getOriginalOrderInput())
            .map(OriginalOrderInput::getOriginalOrderId).orElse(null)) && StrategyOfBookingInitUtil.approvalReuseReBook(
            strategyInfoMap)) {
            return true;
        }
        // 人工沿用
        if (StringUtil.isNotBlank(Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
            .map(ApprovalFlowReuseInput::getArtificialReuseNo).orElse(null))) {
            return true;
        }
        // offline智能推荐沿用
        if (BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
                .map(ApprovalFlowReuseInput::getAiReuse).orElse(null))) {
            return true;
        }
        // 蓝色空间智能沿用新增功能直接全量灰度
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() != PosEnum.CHINA) {
            return true;
        }
        // 国内站灰度智能延用，开关由vo传入
        if (StrategyOfBookingInitUtil.approvalFlowReuseNew(strategyInfoMap)) {
            return true;
        }
        // 二次提交以token中的标识为准判断灰度
        if (orderCreateToken.containsContinueType(ContinueTypeConst.APPROVAL_FLOW_REUSE)) {
            return true;
        }
        return false;
    }

    public static boolean needPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse(
        OrderDetailInfoType orderDetailInfoType) {
        return StringUtil.isNotBlank(
            Optional.ofNullable(orderDetailInfoType).map(OrderDetailInfoType::getOrderBasicInfo)
                .map(OrderBasicInfoType::getPolicyUid).orElse(null));
    }

    public static boolean needGetCityBaseInfoResponseTypeOfApprovalFlowReuse(OrderDetailInfoType orderDetailInfoType) {
        return TemplateNumberUtil.isNotZeroAndNull(
            Optional.ofNullable(orderDetailInfoType).map(OrderDetailInfoType::getHotelInfo)
                .map(HotelInfoType::getHotelAreaInfo).map(HotelAreaInfoType::getCityId).orElse(null));
    }

    public static OrderDetailInfoType buildOrderDetailInfoType(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderDetailResponseType orderDetailResponseTypeOfApprovalFlowReuse,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        if (orderDetailResponseTypeOfApprovalFlowReuse == null || CollectionUtil.isEmpty(
            orderDetailResponseTypeOfApprovalFlowReuse.getOrderDetailInfoList())) {
            return null;
        }
        Long approvalReuseReBookOrderId =
            OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap);
        if (TemplateNumberUtil.isNotZeroAndNull(approvalReuseReBookOrderId)) {
            return orderDetailResponseTypeOfApprovalFlowReuse.getOrderDetailInfoList().stream().filter(
                orderDetailInfoType -> orderDetailInfoType != null && approvalReuseReBookOrderId.equals(
                    orderDetailInfoType.getOrderId())).toList().stream().findFirst().orElse(null);
        }
        Long approvalArtificialReuseNoOrderId =
            OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(orderCreateRequestType,
                getOrderFoundationDataResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(approvalArtificialReuseNoOrderId)) {
            return orderDetailResponseTypeOfApprovalFlowReuse.getOrderDetailInfoList().stream().filter(
                orderDetailInfoType -> orderDetailInfoType != null && approvalArtificialReuseNoOrderId.equals(
                    orderDetailInfoType.getOrderId())).toList().stream().findFirst().orElse(null);
        }
        Long approvalArtificialReuseNoTripIdOfOrderId = Optional.ofNullable(checkHotelAuthExtensionResponseType)
            .map(CheckHotelAuthExtensionResponseType::getOrderId).orElse(null);
        if (TemplateNumberUtil.isNotZeroAndNull(approvalArtificialReuseNoTripIdOfOrderId)) {
            return orderDetailResponseTypeOfApprovalFlowReuse.getOrderDetailInfoList().stream().filter(
                orderDetailInfoType -> orderDetailInfoType != null && approvalArtificialReuseNoTripIdOfOrderId.equals(
                    orderDetailInfoType.getOrderId())).toList().stream().findFirst().orElse(null);
        }
        Long approvalReuseAiBookOrderId =
            OrderCreateProcessorOfUtil.buildApprovalReuseAiBookOrderId(orderCreateRequestType, strategyInfoMap,
                queryHotelAuthExtensionResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(approvalReuseAiBookOrderId)) {
            return orderDetailResponseTypeOfApprovalFlowReuse.getOrderDetailInfoList().stream().filter(
                orderDetailInfoType -> orderDetailInfoType != null && approvalReuseAiBookOrderId.equals(
                    orderDetailInfoType.getOrderId())).toList().stream().findFirst().orElse(null);
        }
        return null;
    }
}
